"""
Intelligent Collection Discovery POC - Main Application
"""

import sys
import traceback
from typing import Dict, Any
import json
from datetime import datetime

from enhanced_cluster_manager import EnhancedMongoClusterManager
from collection_discovery_engine import IntelligentCollectionDiscovery
from business_glossary import ConceptExtractor

class IntelligentDiscoveryPOC:
    """Main POC application for intelligent collection discovery"""
    
    def __init__(self):
        self.cluster_manager = None
        self.discovery_engine = None
        self.is_initialized = False
    
    def initialize(self, connection_string: str) -> bool:
        """Initialize the POC with MongoDB connection"""
        try:
            print("🚀 INTELLIGENT COLLECTION DISCOVERY POC")
            print("="*60)
            
            # Initialize cluster manager
            print("📡 Initializing cluster manager...")
            self.cluster_manager = EnhancedMongoClusterManager(connection_string)
            
            # Connect and analyze cluster
            print("🔗 Connecting to MongoDB cluster...")
            summary = self.cluster_manager.connect_and_analyze_cluster()
            
            if "error" in summary:
                print(f"❌ Failed to connect: {summary['error']}")
                return False
            
            # Initialize discovery engine
            print("🧠 Initializing intelligent discovery engine...")
            self.discovery_engine = IntelligentCollectionDiscovery(self.cluster_manager)
            
            self.is_initialized = True
            
            # Print cluster summary
            self.cluster_manager.print_cluster_summary()
            
            print("\n✅ POC initialized successfully!")
            print("🎯 Ready to analyze queries and discover relevant collections!")
            
            return True
            
        except Exception as e:
            print(f"❌ Initialization failed: {str(e)}")
            traceback.print_exc()
            return False
    
    def analyze_query(self, user_query: str) -> Dict[str, Any]:
        """Analyze a user query and discover relevant collections"""
        if not self.is_initialized:
            return {"error": "POC not initialized. Please run initialize() first."}
        
        try:
            print(f"\n{'='*80}")
            print(f"🔍 QUERY ANALYSIS")
            print(f"{'='*80}")
            
            # Discover relevant collections
            discovery_result = self.discovery_engine.find_relevant_collections(user_query)
            
            # Print detailed results
            self._print_discovery_results(discovery_result)
            
            # Return structured results
            return self._format_results_for_api(discovery_result)
            
        except Exception as e:
            error_msg = f"Query analysis failed: {str(e)}"
            print(f"❌ {error_msg}")
            traceback.print_exc()
            return {"error": error_msg}
    
    def _print_discovery_results(self, result):
        """Print human-readable discovery results"""
        print(f"\n📊 DISCOVERY RESULTS")
        print(f"Query: '{result.query}'")
        print(f"Analysis time: {result.analysis_time:.2f} seconds")
        print(f"Collections analyzed: {result.total_collections_analyzed}")
        print(f"Relevant matches found: {len(result.matches)}")
        
        # Print concept analysis
        print(f"\n🧠 CONCEPT ANALYSIS:")
        concepts = result.concepts
        print(f"   Primary concepts: {concepts['primary_concepts']}")
        print(f"   Intent: {concepts['intent']['type']} (confidence: {concepts['intent']['confidence']})")
        print(f"   Categories: {concepts['categories']}")
        
        # Print top matches
        print(f"\n🏆 TOP COLLECTION MATCHES:")
        for i, match in enumerate(result.matches[:5], 1):  # Top 5 matches
            print(f"\n   {i}. {match.database}.{match.collection}")
            print(f"      Final Score: {match.final_score:.3f} | Confidence: {match.confidence}")
            print(f"      Schema: {match.schema_score:.3f} | Content: {match.content_score:.3f} | Usage: {match.usage_score:.3f}")
            
            # Print reasoning
            if match.reasoning:
                print(f"      Reasoning:")
                for reason in match.reasoning[:3]:  # Top 3 reasons
                    print(f"        • {reason}")
            
            # Print field matches
            if match.field_matches:
                top_fields = sorted(match.field_matches, key=lambda x: x['score'], reverse=True)[:3]
                print(f"      Key Fields:")
                for field in top_fields:
                    print(f"        • {field['field_name']} ({field['field_type']}) - score: {field['score']:.2f}")
        
        # Print recommendations
        if result.recommendations:
            print(f"\n💡 RECOMMENDATIONS:")
            for rec in result.recommendations:
                print(f"   • {rec}")
    
    def _format_results_for_api(self, result) -> Dict[str, Any]:
        """Format results for API consumption"""
        return {
            "query": result.query,
            "analysis_time": result.analysis_time,
            "total_collections_analyzed": result.total_collections_analyzed,
            "concepts": result.concepts,
            "matches": [
                {
                    "database": match.database,
                    "collection": match.collection,
                    "final_score": match.final_score,
                    "confidence": match.confidence,
                    "scores": {
                        "schema": match.schema_score,
                        "content": match.content_score,
                        "usage": match.usage_score
                    },
                    "reasoning": match.reasoning,
                    "field_matches": match.field_matches
                }
                for match in result.matches
            ],
            "recommendations": result.recommendations,
            "timestamp": datetime.now().isoformat()
        }
    
    def run_interactive_demo(self):
        """Run interactive demo mode"""
        if not self.is_initialized:
            print("❌ POC not initialized. Please run initialize() first.")
            return
        
        print(f"\n🎮 INTERACTIVE DEMO MODE")
        print(f"{'='*60}")
        print("Enter natural language queries to discover relevant collections.")
        print("Type 'quit' to exit, 'help' for examples, or 'stats' for cluster info.")
        
        while True:
            try:
                user_input = input("\n🔍 Enter your query: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                
                elif user_input.lower() == 'help':
                    self._show_example_queries()
                    continue
                
                elif user_input.lower() == 'stats':
                    self.cluster_manager.print_cluster_summary()
                    continue
                
                elif not user_input:
                    print("Please enter a query or 'help' for examples.")
                    continue
                
                # Analyze the query
                self.analyze_query(user_input)
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {str(e)}")
    
    def _show_example_queries(self):
        """Show example queries for testing"""
        examples = [
            "Show me customer satisfaction trends",
            "What are our top selling products?",
            "Display user registration patterns",
            "Analyze order fulfillment performance",
            "Create a chart of support ticket resolution times",
            "Show product return rates by category",
            "Find revenue trends over time",
            "Display customer feedback analysis",
            "Show inventory levels by warehouse",
            "Analyze user behavior patterns"
        ]
        
        print(f"\n📝 EXAMPLE QUERIES:")
        for i, example in enumerate(examples, 1):
            print(f"   {i}. {example}")
    
    def run_batch_test(self):
        """Run batch test with predefined queries"""
        if not self.is_initialized:
            print("❌ POC not initialized. Please run initialize() first.")
            return
        
        test_queries = [
            "Show me customer satisfaction trends over the last 6 months",
            "What are our top selling products by revenue?",
            "Display user registration patterns this year",
            "Analyze order fulfillment performance",
            "Create a chart of support ticket resolution times",
            "Show product return rates by category"
        ]
        
        print(f"\n🧪 BATCH TEST MODE")
        print(f"{'='*60}")
        print(f"Testing {len(test_queries)} predefined queries...")
        
        results = []
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n📝 Test {i}/{len(test_queries)}: '{query}'")
            result = self.analyze_query(query)
            results.append(result)
            
            # Brief summary
            if "error" not in result:
                matches = result.get("matches", [])
                if matches:
                    top_match = matches[0]
                    print(f"   ✅ Top match: {top_match['database']}.{top_match['collection']} (score: {top_match['final_score']:.3f})")
                else:
                    print(f"   ⚠️ No relevant collections found")
            else:
                print(f"   ❌ Error: {result['error']}")
        
        print(f"\n📊 BATCH TEST SUMMARY:")
        successful_tests = len([r for r in results if "error" not in r and r.get("matches")])
        print(f"   Successful discoveries: {successful_tests}/{len(test_queries)}")
        print(f"   Success rate: {successful_tests/len(test_queries)*100:.1f}%")


def main():
    """Main function to run the POC"""
    print("🚀 Welcome to Intelligent Collection Discovery POC!")

    # Get MongoDB connection string
    connection_string = input("Enter MongoDB connection string (or press Enter for Atlas): ").strip()
    if not connection_string:
        # Default to your Atlas cluster
        connection_string = "mongodb+srv://pkarthikeyan1202:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"
        print("🔗 Using Atlas cluster connection")
    
    # Initialize POC
    poc = IntelligentDiscoveryPOC()
    
    if not poc.initialize(connection_string):
        print("❌ Failed to initialize POC. Exiting.")
        return
    
    # Show menu
    while True:
        print(f"\n🎯 CHOOSE AN OPTION:")
        print("1. Interactive Demo (enter queries manually)")
        print("2. Batch Test (test with predefined queries)")
        print("3. Single Query Test")
        print("4. Show Cluster Stats")
        print("5. Exit")
        
        choice = input("\nEnter your choice (1-5): ").strip()
        
        if choice == '1':
            poc.run_interactive_demo()
        elif choice == '2':
            poc.run_batch_test()
        elif choice == '3':
            query = input("Enter your query: ").strip()
            if query:
                poc.analyze_query(query)
        elif choice == '4':
            poc.cluster_manager.print_cluster_summary()
        elif choice == '5':
            print("👋 Goodbye!")
            break
        else:
            print("Invalid choice. Please enter 1-5.")


if __name__ == "__main__":
    main()
