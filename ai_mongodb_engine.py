from dotenv import load_dotenv
from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnablePassthrough
from langchain_core.output_parsers import <PERSON><PERSON><PERSON>ut<PERSON>Pars<PERSON>
from langchain_anthropic import <PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain_openai import ChatOpenAI
import streamlit as st
import pymongo
from pymongo import MongoClient
import json
import logging
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MongoDatabase:
    """MongoDB database wrapper - mirrors SQLDatabase exactly"""
    
    def __init__(self, connection_string: str, database_name: str):
        self.connection_string = connection_string
        self.database_name = database_name
        self.client = None
        self.database = None
        
    @classmethod
    def from_uri(cls, connection_string: str, database_name: str):
        """Create from URI - mirrors SQLDatabase.from_uri"""
        return cls(connection_string, database_name)
    
    def _connect(self):
        """Establish connection"""
        if self.client is None:
            self.client = MongoClient(self.connection_string, serverSelectionTimeoutMS=10000)
            self.client.admin.command('ping')  # Test connection
            self.database = self.client[self.database_name]
    
    def get_table_info(self) -> str:
        """Get database schema - mirrors SQLDatabase.get_table_info()"""
        try:
            self._connect()
            collections = self.database.list_collection_names()
            
            if not collections:
                return f"Database '{self.database_name}' has no collections."
            
            schema_info = f"Database: {self.database_name}\nCollections:\n\n"
            
            for collection_name in collections:
                try:
                    collection = self.database[collection_name]
                    count = collection.count_documents({})
                    schema_info += f"Collection: {collection_name} ({count} documents)\n"
                    
                    # Get sample document to show structure
                    if count > 0:
                        sample = collection.find_one()
                        if sample:
                            # Show field names and sample values
                            fields = list(sample.keys())
                            schema_info += f"Sample fields: {', '.join(fields[:10])}\n"
                            
                            # Show sample document structure
                            sample_doc = {}
                            for key, value in sample.items():
                                if isinstance(value, dict):
                                    sample_doc[key] = "{object}"
                                elif isinstance(value, list):
                                    sample_doc[key] = f"[array({len(value)})]"
                                elif isinstance(value, str):
                                    sample_doc[key] = f'"{str(value)[:30]}..."' if len(str(value)) > 30 else f'"{value}"'
                                else:
                                    sample_doc[key] = value
                            
                            schema_info += f"Sample document: {json.dumps(sample_doc, default=str)}\n"
                    
                    schema_info += "\n"
                except Exception as e:
                    schema_info += f"Collection: {collection_name} (Error: {str(e)})\n\n"
            
            # Print complete schema for debugging
            print("=" * 80)
            print("COMPLETE DATABASE SCHEMA:")
            print("=" * 80)
            print(schema_info)
            print("=" * 80)
            
            return schema_info
            
        except Exception as e:
            error_msg = f"Error getting schema: {str(e)}"
            print(f"SCHEMA ERROR: {error_msg}")
            return error_msg
    
    def run(self, query: str) -> str:
        """Execute MongoDB operation - mirrors SQLDatabase.run()"""
        try:
            self._connect()
            
            # Print the query being executed
            print("=" * 80)
            print("EXECUTING PYMONGO QUERY:")
            print("=" * 80)
            print(f"Query: {query}")
            print("=" * 80)
            
            # Execute the PyMongo code generated by AI
            # Create safe execution environment
            safe_globals = {
                'db': self.database,
                'list': list,
                'json': json,
                '__builtins__': {}
            }
            
            # Execute AI-generated PyMongo code
            result = eval(query, safe_globals, {})
            
            # Format result
            if isinstance(result, list):
                formatted_result = json.dumps(result, default=str, indent=2)
            else:
                formatted_result = str(result)
            
            # Print the execution result
            print("EXECUTION RESULT:")
            print("=" * 80)
            print(formatted_result)
            print("=" * 80)
            
            return formatted_result
                
        except Exception as e:
            error_msg = f"Error: {str(e)}"
            print(f"EXECUTION ERROR: {error_msg}")
            return error_msg

def init_database(connection_string: str, database_name: str) -> MongoDatabase:
    """Initialize MongoDB - mirrors the MySQL init_database function exactly"""
    return MongoDatabase.from_uri(connection_string, database_name)

def get_mongo_chain(db):
    """Get MongoDB chain - mirrors get_sql_chain exactly"""
    template = """
    You are a data analyst at a company. You are interacting with a user who is asking you questions about the company's database.
    Based on the database schema below, write PyMongo code that would answer the user's question. Take the conversation history into account.
    
    <SCHEMA>{schema}</SCHEMA>
    
    Conversation History: {chat_history}
    
    Write only the PyMongo code and nothing else. Do not wrap the code in any other text, not even backticks.
    Use 'db' as the database object.
    
    For example:
    Question: which 3 collections have the most documents?
    db.list_collection_names()
    Question: show me 10 products
    list(db.products.find().limit(10))
    Question: count all users
    db.users.count_documents({{}})
    
    Your turn:
    
    Question: {question}
    PyMongo Code:
    """
    
    prompt = ChatPromptTemplate.from_template(template)
    
    llm = ChatAnthropic(model="claude-3-sonnet-********", temperature=0)
    
    def get_schema(_):
        return db.get_table_info()
    
    return (
        RunnablePassthrough.assign(schema=get_schema)
        | prompt
        | llm
        | StrOutputParser()
    )

def get_response(user_query: str, db: MongoDatabase, chat_history: list):
    """Get response - mirrors the MySQL get_response function exactly"""
    print("=" * 80)
    print("PROCESSING USER QUERY:")
    print("=" * 80)
    print(f"User Question: {user_query}")
    print("=" * 80)
    
    mongo_chain = get_mongo_chain(db)
    
    template = """
    You are a data analyst at a company. You are interacting with a user who is asking you questions about the company's database.
    Based on the database schema below, PyMongo code, and execution result, write a natural language response.
    <SCHEMA>{schema}</SCHEMA>

    Conversation History: {chat_history}
    PyMongo Code: <CODE>{query}</CODE>
    User question: {question}
    Execution Result: {response}"""
    
    prompt = ChatPromptTemplate.from_template(template)
    
    llm = ChatAnthropic(model="claude-3-sonnet-********", temperature=0)
    
    def get_query_with_logging(vars):
        """Generate query with logging"""
        mongo_chain = get_mongo_chain(db)
        schema = db.get_table_info()
        
        query = mongo_chain.invoke({
            "question": user_query,
            "chat_history": chat_history,
            "schema": schema
        })
        
        print("AI GENERATED PYMONGO CODE:")
        print("=" * 80)
        print(f"Generated Query: {query}")
        print("=" * 80)
        
        return query
    
    def get_response_with_logging(vars):
        """Execute query with logging"""
        query = vars["query"]
        result = db.run(query)
        return result
    
    chain = (
        RunnablePassthrough.assign(query=get_query_with_logging).assign(
            schema=lambda _: db.get_table_info(),
            response=get_response_with_logging,
        )
        | prompt
        | llm
        | StrOutputParser()
    )
    
    final_response = chain.invoke({
        "question": user_query,
        "chat_history": chat_history,
    })
    
    print("FINAL AI RESPONSE:")
    print("=" * 80)
    print(final_response)
    print("=" * 80)
    
    return final_response

# Initialize session state
if "chat_history" not in st.session_state:
    st.session_state.chat_history = [
        AIMessage(content="Hello! I'm a MongoDB assistant. Connect to your database and start asking questions."),
    ]

load_dotenv()

st.set_page_config(page_title="Chat with MongoDB", page_icon="🍃")
st.title("Chat with MongoDB")

# Sidebar - exactly like MySQL version
with st.sidebar:
    st.subheader("Settings")
    st.write("This is a simple chat application using MongoDB. Connect to the database and start chatting.")
    
    # Connection method
    connection_method = st.radio(
        "Connection Method:",
        ["Connection String", "Individual Parameters"]
    )
    
    if connection_method == "Connection String":
        connection_string = st.text_input(
            "MongoDB Connection String", 
            value="mongodb://localhost:27017/", 
            key="ConnectionString"
        )
    else:
        host = st.text_input("Host", value="localhost", key="Host")
        port = st.text_input("Port", value="27017", key="Port")
        user = st.text_input("User", value="", key="User")
        password = st.text_input("Password", type="password", value="", key="Password")
        
        if user and password:
            connection_string = f"mongodb://{user}:{password}@{host}:{port}/"
        else:
            connection_string = f"mongodb://{host}:{port}/"
    
    database_name = st.text_input("Database", value="test", key="Database")
    
    if st.button("Connect"):
        with st.spinner("Connecting to database..."):
            try:
                print("=" * 100)
                print("INITIATING DATABASE CONNECTION")
                print("=" * 100)
                print(f"Connection String: {connection_string}")
                print(f"Database Name: {database_name}")
                print("=" * 100)
                
                db = init_database(connection_string, database_name)
                # Test the connection by getting schema
                schema_info = db.get_table_info()
                st.session_state.db = db
                st.success("Connected to database!")
                
                # Show schema
                with st.expander("Database Schema"):
                    st.text(schema_info)
                    
            except Exception as e:
                error_msg = f"Connection failed: {str(e)}"
                print(f"CONNECTION ERROR: {error_msg}")
                st.error(error_msg)

# Chat interface - exactly like MySQL version
for message in st.session_state.chat_history:
    if isinstance(message, AIMessage):
        with st.chat_message("AI"):
            st.markdown(message.content)
    elif isinstance(message, HumanMessage):
        with st.chat_message("Human"):
            st.markdown(message.content)

user_query = st.chat_input("Type a message...")
if user_query is not None and user_query.strip() != "":
    st.session_state.chat_history.append(HumanMessage(content=user_query))
    
    with st.chat_message("Human"):
        st.markdown(user_query)
        
    with st.chat_message("AI"):
        if 'db' not in st.session_state:
            response = "Please connect to a database first."
            st.markdown(response)
        else:
            try:
                print("\n" + "=" * 100)
                print("STARTING QUERY PROCESSING PIPELINE")
                print("=" * 100)
                
                response = get_response(user_query, st.session_state.db, st.session_state.chat_history)
                st.markdown(response)
                
                print("QUERY PROCESSING COMPLETED")
                print("=" * 100)
                
            except Exception as e:
                response = f"Sorry, I encountered an error: {str(e)}"
                print(f"PIPELINE ERROR: {response}")
                st.markdown(response)
        
        st.session_state.chat_history.append(AIMessage(content=response))