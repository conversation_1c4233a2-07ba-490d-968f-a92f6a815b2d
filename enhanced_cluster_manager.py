"""
Enhanced MongoDB Cluster Manager with Intelligent Collection Discovery
"""

import pymongo
from pymongo import MongoClient
from typing import Dict, List, Any, Optional, Tuple
import json
import logging
from dataclasses import dataclass, field
from datetime import datetime
import time
import traceback

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class FieldInfo:
    """Information about a field in a collection"""
    name: str
    type: str
    sample_values: List[Any] = field(default_factory=list)
    is_indexed: bool = False
    frequency: float = 0.0  # How often this field appears in documents

@dataclass
class CollectionInfo:
    """Comprehensive information about a collection"""
    database: str
    name: str
    document_count: int
    estimated_size: int
    fields: Dict[str, FieldInfo] = field(default_factory=dict)
    sample_documents: List[Dict] = field(default_factory=list)
    indexes: List[str] = field(default_factory=list)
    last_analyzed: datetime = field(default_factory=datetime.now)
    collection_object: Any = None  # Reference to actual PyMongo collection

@dataclass
class DatabaseInfo:
    """Information about a database"""
    name: str
    collections: Dict[str, CollectionInfo] = field(default_factory=dict)
    total_documents: int = 0
    estimated_size: int = 0
    relationships: List[str] = field(default_factory=list)

@dataclass
class ClusterInfo:
    """Information about the entire cluster"""
    connection_string: str
    databases: Dict[str, DatabaseInfo] = field(default_factory=dict)
    total_collections: int = 0
    total_documents: int = 0
    cluster_type: str = "unknown"  # standalone, replica_set, sharded
    last_analyzed: datetime = field(default_factory=datetime.now)

class EnhancedMongoClusterManager:
    """Enhanced MongoDB cluster manager with intelligent analysis"""
    
    def __init__(self, connection_string: str):
        self.connection_string = connection_string
        self.client: Optional[MongoClient] = None
        self.cluster_info = ClusterInfo(connection_string=connection_string)
        self.is_connected = False
        
    def connect_and_analyze_cluster(self) -> Dict[str, Any]:
        """Connect to cluster and perform comprehensive analysis"""
        try:
            print("🔗 Connecting to MongoDB cluster...")
            
            # Establish connection
            self.client = MongoClient(
                self.connection_string, 
                serverSelectionTimeoutMS=10000,
                connectTimeoutMS=10000
            )
            
            # Test connection
            self.client.admin.command('ping')
            self.is_connected = True
            
            print("✅ Connected successfully!")
            
            # Analyze cluster topology
            self._analyze_cluster_topology()
            
            # Discover and analyze databases
            self._discover_and_analyze_databases()
            
            # Generate summary
            summary = self._generate_cluster_summary()
            
            print("🎉 Cluster analysis completed!")
            return summary
            
        except Exception as e:
            error_msg = f"❌ Failed to connect to cluster: {str(e)}"
            logger.error(error_msg)
            print(error_msg)
            return {"error": error_msg}
    
    def _analyze_cluster_topology(self):
        """Analyze cluster topology and configuration"""
        try:
            # Get server info
            server_info = self.client.server_info()
            
            # Determine cluster type
            try:
                replica_status = self.client.admin.command("replSetGetStatus")
                self.cluster_info.cluster_type = "replica_set"
            except:
                try:
                    shard_status = self.client.admin.command("listShards")
                    self.cluster_info.cluster_type = "sharded"
                except:
                    self.cluster_info.cluster_type = "standalone"
            
            print(f"📊 Cluster type: {self.cluster_info.cluster_type}")
            print(f"📊 MongoDB version: {server_info.get('version', 'unknown')}")
            
        except Exception as e:
            logger.warning(f"Could not analyze cluster topology: {str(e)}")
    
    def _discover_and_analyze_databases(self):
        """Discover and analyze all databases in the cluster"""
        try:
            # Get all database names
            all_db_names = self.client.list_database_names()
            
            # Filter out system databases
            user_db_names = [
                db for db in all_db_names 
                if db not in ['admin', 'local', 'config']
            ]
            
            print(f"🗄️ Found {len(user_db_names)} user databases: {user_db_names}")
            
            # Analyze each database
            for db_name in user_db_names:
                print(f"   📋 Analyzing database: {db_name}")
                db_info = self._analyze_database(db_name)
                self.cluster_info.databases[db_name] = db_info
                self.cluster_info.total_documents += db_info.total_documents
            
            self.cluster_info.total_collections = sum(
                len(db_info.collections) for db_info in self.cluster_info.databases.values()
            )
            
        except Exception as e:
            logger.error(f"Error discovering databases: {str(e)}")
            raise
    
    def _analyze_database(self, db_name: str) -> DatabaseInfo:
        """Perform deep analysis of a single database"""
        try:
            db = self.client[db_name]
            collection_names = db.list_collection_names()
            
            db_info = DatabaseInfo(name=db_name)
            
            print(f"      📁 Found {len(collection_names)} collections")
            
            # Analyze each collection
            for collection_name in collection_names:
                try:
                    print(f"         🔍 Analyzing collection: {collection_name}")
                    collection_info = self._analyze_collection(db[collection_name], db_name, collection_name)
                    db_info.collections[collection_name] = collection_info
                    db_info.total_documents += collection_info.document_count
                    
                except Exception as e:
                    logger.warning(f"Error analyzing collection {collection_name}: {str(e)}")
                    # Create minimal collection info
                    db_info.collections[collection_name] = CollectionInfo(
                        database=db_name,
                        name=collection_name,
                        document_count=0,
                        estimated_size=0
                    )
            
            return db_info
            
        except Exception as e:
            logger.error(f"Error analyzing database {db_name}: {str(e)}")
            raise
    
    def _analyze_collection(self, collection, db_name: str, collection_name: str) -> CollectionInfo:
        """Perform comprehensive analysis of a single collection"""
        try:
            # Get basic stats
            doc_count = collection.estimated_document_count()
            
            # Get collection stats (if available)
            try:
                stats = collection.database.command("collStats", collection_name)
                estimated_size = stats.get('size', 0)
            except:
                estimated_size = 0
            
            # Create collection info
            collection_info = CollectionInfo(
                database=db_name,
                name=collection_name,
                document_count=doc_count,
                estimated_size=estimated_size,
                collection_object=collection
            )
            
            # Get indexes
            try:
                indexes = list(collection.list_indexes())
                collection_info.indexes = [idx.get('name', 'unknown') for idx in indexes]
            except:
                collection_info.indexes = []
            
            # Sample documents for field analysis (limit to avoid performance issues)
            if doc_count > 0:
                sample_size = min(50, doc_count)  # Sample up to 50 documents
                sample_docs = list(collection.aggregate([{"$sample": {"size": sample_size}}]))
                collection_info.sample_documents = sample_docs
                
                # Analyze fields from sample documents
                collection_info.fields = self._analyze_fields(sample_docs)
            
            print(f"            ✅ {doc_count} documents, {len(collection_info.fields)} fields")
            
            return collection_info
            
        except Exception as e:
            logger.error(f"Error analyzing collection {collection_name}: {str(e)}")
            # Return minimal info on error
            return CollectionInfo(
                database=db_name,
                name=collection_name,
                document_count=0,
                estimated_size=0,
                collection_object=collection
            )
    
    def _analyze_fields(self, sample_documents: List[Dict]) -> Dict[str, FieldInfo]:
        """Analyze fields from sample documents"""
        field_analysis = {}
        total_docs = len(sample_documents)
        
        if total_docs == 0:
            return field_analysis
        
        # Analyze each document
        for doc in sample_documents:
            self._analyze_document_fields(doc, field_analysis, "", total_docs)
        
        # Calculate frequencies
        for field_info in field_analysis.values():
            field_info.frequency = len(field_info.sample_values) / total_docs
        
        return field_analysis
    
    def _analyze_document_fields(self, doc: Dict, field_analysis: Dict, prefix: str, total_docs: int):
        """Recursively analyze fields in a document"""
        for key, value in doc.items():
            field_name = f"{prefix}.{key}" if prefix else key
            
            # Skip MongoDB internal fields for now
            if key.startswith('_') and key != '_id':
                continue
            
            # Initialize field info if not exists
            if field_name not in field_analysis:
                field_analysis[field_name] = FieldInfo(
                    name=field_name,
                    type=type(value).__name__,
                    sample_values=[]
                )
            
            field_info = field_analysis[field_name]
            
            # Add sample value (limit to avoid memory issues)
            if len(field_info.sample_values) < 10:
                if isinstance(value, (str, int, float, bool)):
                    field_info.sample_values.append(value)
                elif isinstance(value, list) and len(value) > 0:
                    field_info.sample_values.append(f"[array({len(value)})]")
                elif isinstance(value, dict):
                    field_info.sample_values.append("{object}")
                else:
                    field_info.sample_values.append(str(type(value).__name__))
            
            # For nested objects, analyze recursively (limit depth)
            if isinstance(value, dict) and not prefix.count('.') > 2:  # Limit nesting depth
                self._analyze_document_fields(value, field_analysis, field_name, total_docs)
    
    def _generate_cluster_summary(self) -> Dict[str, Any]:
        """Generate comprehensive cluster summary"""
        summary = {
            "cluster_type": self.cluster_info.cluster_type,
            "total_databases": len(self.cluster_info.databases),
            "total_collections": self.cluster_info.total_collections,
            "total_documents": self.cluster_info.total_documents,
            "databases": {}
        }
        
        for db_name, db_info in self.cluster_info.databases.items():
            summary["databases"][db_name] = {
                "collections": len(db_info.collections),
                "documents": db_info.total_documents,
                "collection_details": {
                    coll_name: {
                        "documents": coll_info.document_count,
                        "fields": len(coll_info.fields),
                        "indexes": len(coll_info.indexes)
                    }
                    for coll_name, coll_info in db_info.collections.items()
                }
            }
        
        return summary
    
    def get_all_collections(self) -> List[CollectionInfo]:
        """Get list of all collections across all databases"""
        all_collections = []
        
        for db_info in self.cluster_info.databases.values():
            for collection_info in db_info.collections.values():
                all_collections.append(collection_info)
        
        return all_collections
    
    def get_collection_info(self, database: str, collection: str) -> Optional[CollectionInfo]:
        """Get information about a specific collection"""
        if database in self.cluster_info.databases:
            return self.cluster_info.databases[database].collections.get(collection)
        return None
    
    def print_cluster_summary(self):
        """Print a human-readable cluster summary"""
        print("\n" + "="*80)
        print("🏢 MONGODB CLUSTER ANALYSIS SUMMARY")
        print("="*80)
        
        print(f"🔗 Connection: {self.connection_string}")
        print(f"📊 Cluster Type: {self.cluster_info.cluster_type}")
        print(f"🗄️ Databases: {len(self.cluster_info.databases)}")
        print(f"📁 Collections: {self.cluster_info.total_collections}")
        print(f"📄 Documents: {self.cluster_info.total_documents:,}")
        
        print(f"\n📋 DATABASE BREAKDOWN:")
        for db_name, db_info in self.cluster_info.databases.items():
            print(f"   🗄️ {db_name}")
            print(f"      📁 Collections: {len(db_info.collections)}")
            print(f"      📄 Documents: {db_info.total_documents:,}")
            
            # Show top collections by document count
            sorted_collections = sorted(
                db_info.collections.values(),
                key=lambda x: x.document_count,
                reverse=True
            )
            
            for coll_info in sorted_collections[:3]:  # Top 3 collections
                print(f"         📦 {coll_info.name}: {coll_info.document_count:,} docs, {len(coll_info.fields)} fields")
        
        print("="*80)


if __name__ == "__main__":
    # Test the cluster manager
    connection_string = input("Enter MongoDB connection string (or press Enter for localhost): ").strip()
    if not connection_string:
        connection_string = "mongodb://localhost:27017/"
    
    cluster_manager = EnhancedMongoClusterManager(connection_string)
    
    try:
        summary = cluster_manager.connect_and_analyze_cluster()
        cluster_manager.print_cluster_summary()
        
        print(f"\n✅ Analysis complete! Found {len(cluster_manager.get_all_collections())} collections to work with.")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        traceback.print_exc()
