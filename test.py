from dotenv import load_dotenv
from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.runnables import RunnablePassthrough
from langchain_core.output_parsers import Str<PERSON><PERSON><PERSON><PERSON>arser
from langchain_anthropic import <PERSON>t<PERSON><PERSON><PERSON>
from langchain_openai import ChatOpenAI
import streamlit as st
import pymongo
from pymongo import MongoClient
import json
import logging
from typing import Dict, Any, List, Optional, Tuple
import time
import traceback
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from enum import Enum
import re
from datetime import datetime, timedelta
import random
import uuid
import os
from pathlib import Path

# Additional imports for enhanced features
try:
    import plotly.express as px
    import plotly.graph_objects as go
    import pandas as pd
    VISUALIZATION_AVAILABLE = True
except ImportError:
    VISUALIZATION_AVAILABLE = False
    st.warning("⚠️ Install plotly pandas for visualization: pip install plotly pandas")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ==================== AUTONOMOUS MONGODB SYSTEM ====================

# Modified AutonomousMongoSystem class to remove auto-connect and hardcoded simulation
class AutonomousMongoSystem:
    """MongoDB system that works with any database via explicit connection"""
    
    def __init__(self):
        self.client = None
        self.database = None
        self.current_db_name = None
        self.schema_cache = {}
        self.is_connected = False
        self.last_query = ""
        self.last_result = ""
    
    def connect(self, connection_string: str, database_name: str) -> bool:
        """Connect to a MongoDB database with any connection string"""
        try:
            print(f"🔍 Connecting to: {connection_string}")
            client = MongoClient(connection_string, serverSelectionTimeoutMS=5000)
            # Test connection
            client.admin.command('ping')
            
            # Connect to the specified database
            self.client = client
            self.current_db_name = database_name
            self.database = client[database_name]
            self.is_connected = True
            print(f"✅ Connected to: {self.current_db_name}")
            return True
        except Exception as e:
            print(f"❌ Connection failed: {str(e)}")
            return False
    
    def get_schema_info(self) -> str:
        """Get comprehensive schema information"""
        if not self.is_connected:
            return "No database connection. Please connect to a MongoDB database first."
        
        try:
            # Get all databases
            all_dbs = self.client.list_database_names()
            user_dbs = [db for db in all_dbs if db not in ['admin', 'local', 'config']]
            
            schema_info = f"🏢 MongoDB Instance - {len(user_dbs)} databases available\n\n"
            
            # Current database info
            schema_info += f"📊 Current Database: {self.current_db_name}\n"
            collections = self.database.list_collection_names()
            schema_info += f"📁 Collections: {len(collections)}\n\n"
            
            # Collection details
            for collection_name in collections[:10]:  # Limit to 10 collections
                try:
                    collection = self.database[collection_name]
                    count = collection.estimated_document_count()
                    schema_info += f"📦 {collection_name} ({count} documents)\n"
                    
                    # Get sample document
                    sample = collection.find_one()
                    if sample:
                        # Show key fields
                        fields = list(sample.keys())[:8]
                        schema_info += f"   Fields: {', '.join(fields)}\n"
                        
                        # Show sample values
                        sample_simplified = {}
                        for key in fields:
                            value = sample.get(key)
                            if isinstance(value, dict):
                                sample_simplified[key] = "{object}"
                            elif isinstance(value, list):
                                sample_simplified[key] = f"[{len(value)} items]"
                            else:
                                sample_simplified[key] = str(value)[:30]
                        
                        schema_info += f"   Sample: {sample_simplified}\n"
                    
                    schema_info += "\n"
                    
                except Exception as e:
                    schema_info += f"📦 {collection_name} (Error: {str(e)})\n\n"
            
            # Other available databases
            if len(user_dbs) > 1:
                other_dbs = [db for db in user_dbs if db != self.current_db_name]
                schema_info += f"🗂️ Other Databases: {', '.join(other_dbs)}\n"
            
            return schema_info
            
        except Exception as e:
            return f"❌ Error getting schema: {str(e)}"
    
    def switch_database(self, database_name: str) -> bool:
        """Switch to a different database on the same connection"""
        if not self.client:
            return False
        
        try:
            # Switch to the new database
            self.database = self.client[database_name]
            self.current_db_name = database_name
            
            # Test the connection
            self.database.list_collection_names()
            
            print(f"✅ Switched to database: {database_name}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to switch database: {str(e)}")
            return False
    
    def get_available_databases(self) -> List[str]:
        """Get list of available databases"""
        if not self.client:
            return []
        
        try:
            dbs = self.client.list_database_names()
            return [db for db in dbs if db not in ['admin', 'local', 'config']]
        except Exception as e:
            print(f"❌ Failed to get databases: {str(e)}")
            return []
    
    def execute_query(self, query: str) -> str:
        """Execute MongoDB query with enhanced capabilities"""
        if not self.is_connected:
            return "❌ No database connection. Please connect to a MongoDB database first."
            
        try:
            print(f"\n🔍 EXECUTING QUERY: {query}")
            
            # Enhanced safe execution environment
            safe_globals = {
                'db': self.database,
                'client': self.client,
                'list': list,
                'json': json,
                'len': len,
                'str': str,
                'int': int,
                'float': float,
                'dict': dict,
                'set': set,
                'tuple': tuple,
                'range': range,
                'random': random,
                'uuid': uuid,
                'datetime': datetime,
                'print': lambda *args: None,
                '__builtins__': {
                    'range': range,
                    'len': len,
                    'str': str,
                    'int': int,
                    'float': float,
                    'list': list,
                    'dict': dict,
                    'sum': sum,
                    'max': max,
                    'min': min,
                    'sorted': sorted,
                    'enumerate': enumerate,
                    'zip': zip,
                }
            }
            
            # Real database execution
            result = eval(query, safe_globals, {})
            
            # Handle different result types
            if isinstance(result, list):
                formatted_result = json.dumps(result, default=str, indent=2)
            elif hasattr(result, 'inserted_ids'):
                # Handle insert operations
                formatted_result = f"✅ Successfully inserted {len(result.inserted_ids)} documents"
            elif hasattr(result, 'modified_count'):
                # Handle update operations
                formatted_result = f"✅ Successfully updated {result.modified_count} documents"
            elif hasattr(result, 'deleted_count'):
                # Handle delete operations
                formatted_result = f"✅ Successfully deleted {result.deleted_count} documents"
            elif hasattr(result, 'name'):
                # Handle collection creation
                formatted_result = f"✅ Successfully created collection: {result.name}"
            elif isinstance(result, (str, int, float, bool)):
                formatted_result = str(result)
            else:
                # For other MongoDB objects, convert to string
                formatted_result = str(result)
            
            self.last_query = query
            self.last_result = formatted_result
            return formatted_result
                
        except Exception as e:
            error_msg = f"❌ Query Error: {str(e)}"
            print(f"QUERY ERROR: {error_msg}")
            return error_msg
# ==================== ENHANCED VISUALIZATION ====================
class SmartVisualizationDetector:
    """Intelligent visualization detection and generation"""
    
    VISUALIZATION_KEYWORDS = [
        'chart', 'graph', 'plot', 'visualization', 'visualize', 'visual', 'show',
        'bar chart', 'line chart', 'pie chart', 'histogram', 'scatter plot',
        'trend', 'distribution', 'comparison', 'display', 'draw', 'create',
        'analytics', 'dashboard', 'report', 'breakdown', 'summary', 'by'
    ]
    
    @classmethod
    def needs_visualization(cls, query: str) -> bool:
        """Smart detection of visualization needs"""
        if not VISUALIZATION_AVAILABLE:
            return False
        
        query_lower = query.lower()
        
        # Direct visualization requests
        has_viz_keywords = any(keyword in query_lower for keyword in cls.VISUALIZATION_KEYWORDS)
        
        # Implicit visualization needs
        has_grouping = any(word in query_lower for word in ['by', 'group', 'count', 'sum', 'average'])
        has_comparison = any(word in query_lower for word in ['compare', 'vs', 'versus', 'between'])
        has_analysis = any(word in query_lower for word in ['analyze', 'analysis', 'insight', 'pattern'])
        
        result = has_viz_keywords or has_grouping or has_comparison or has_analysis
        print(f"🎨 Visualization needed: {result} (viz={has_viz_keywords}, group={has_grouping}, comp={has_comparison}, analysis={has_analysis})")
        
        return result

class AdvancedVisualizationGenerator:
    """Advanced visualization generation"""
    
    def create_visualization(self, data: List[Dict], chart_type: str = "bar", 
                           title: str = "Data Visualization") -> Optional[go.Figure]:
        """Create advanced visualizations"""
        if not VISUALIZATION_AVAILABLE or not data:
            print("❌ No visualization available or no data")
            return None
        
        try:
            print(f"📊 Creating {chart_type} chart with {len(data)} data points")
            print(f"🔍 Sample data: {data[0] if data else 'None'}")
            
            # Convert to DataFrame
            df = pd.DataFrame(data)
            df = self._clean_dataframe(df)
            
            if df.empty:
                print("❌ No data available after cleaning")
                return None
            
            print(f"📋 Cleaned DataFrame shape: {df.shape}")
            print(f"📋 Columns: {list(df.columns)}")
            print(f"📋 Data types: {df.dtypes.to_dict()}")
            
            # Create visualization based on type
            if chart_type == "bar":
                fig = self._create_advanced_bar_chart(df, title)
            elif chart_type == "line":
                fig = self._create_advanced_line_chart(df, title)
            elif chart_type == "pie":
                fig = self._create_advanced_pie_chart(df, title)
            elif chart_type == "scatter":
                fig = self._create_advanced_scatter_plot(df, title)
            else:
                fig = self._create_advanced_bar_chart(df, title)
            
            if fig:
                print("✅ Visualization created successfully")
            else:
                print("❌ Failed to create visualization")
            
            return fig
            
        except Exception as e:
            print(f"❌ Visualization error: {str(e)}")
            traceback.print_exc()
            return None
    
    def _clean_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Enhanced data cleaning"""
        try:
            print(f"🧹 Cleaning DataFrame with shape: {df.shape}")
            
            # Remove MongoDB-specific fields
            df = df.loc[:, ~df.columns.str.contains('_id', case=False)]
            
            # Handle missing values
            df = df.fillna('N/A')
            
            # Convert numeric strings to numbers
            for col in df.columns:
                if df[col].dtype == 'object':
                    # Try to convert to numeric
                    try:
                        # First, check if it's all numeric
                        numeric_series = pd.to_numeric(df[col], errors='coerce')
                        if not numeric_series.isna().all():
                            df[col] = numeric_series
                    except:
                        pass
            
            # Remove rows where all values are NaN
            df = df.dropna(how='all')
            
            # For visualization, limit the number of rows
            if len(df) > 100:
                print(f"📊 Limiting data to top 100 rows (from {len(df)})")
                df = df.head(100)
            
            print(f"✅ DataFrame cleaned. Final shape: {df.shape}")
            print(f"📊 Column types: {df.dtypes.to_dict()}")
            
            return df
            
        except Exception as e:
            print(f"❌ Error cleaning dataframe: {str(e)}")
            return pd.DataFrame()  # Return empty DataFrame on error
    
    def _create_advanced_bar_chart(self, df: pd.DataFrame, title: str) -> go.Figure:
        """Create advanced bar chart"""
        try:
            print(f"📊 Creating bar chart with data: {df.shape}")
            print(f"📊 Columns: {list(df.columns)}")
            
            if len(df.columns) >= 2:
                # Find the best columns for x and y
                x_col = None
                y_col = None
                
                # Look for a good categorical column for x-axis
                for col in df.columns:
                    if not pd.api.types.is_numeric_dtype(df[col]):
                        x_col = col
                        break
                
                # Look for a good numeric column for y-axis
                for col in df.columns:
                    if pd.api.types.is_numeric_dtype(df[col]) and col != x_col:
                        y_col = col
                        break
                
                # Fallback to first two columns
                if x_col is None:
                    x_col = df.columns[0]
                if y_col is None:
                    y_col = df.columns[1] if len(df.columns) > 1 else df.columns[0]
                
                # Ensure y values are numeric
                if not pd.api.types.is_numeric_dtype(df[y_col]):
                    df[y_col] = pd.to_numeric(df[y_col], errors='coerce')
                    df = df.dropna(subset=[y_col])
                
                # Sort by y values for better visualization
                df = df.sort_values(by=y_col, ascending=False)
                
                # Limit to top 15 for readability
                if len(df) > 15:
                    df = df.head(15)
                
                fig = px.bar(
                    df, 
                    x=x_col, 
                    y=y_col, 
                    title=title, 
                    height=500,
                    color=y_col,
                    color_continuous_scale='Blues'
                )
                
                # Improve layout
                fig.update_layout(
                    xaxis_tickangle=-45,
                    showlegend=False,
                    xaxis_title=x_col.replace('_', ' ').title(),
                    yaxis_title=y_col.replace('_', ' ').title()
                )
                
                # Add value labels on bars
                fig.update_traces(
                    texttemplate='%{y}',
                    textposition='outside',
                    hovertemplate='<b>%{x}</b><br>%{y}<extra></extra>'
                )
                
                return fig
                
            else:
                # Single column - create value counts
                col = df.columns[0]
                value_counts = df[col].value_counts()
                
                # Limit to top 15
                if len(value_counts) > 15:
                    value_counts = value_counts.head(15)
                
                fig = px.bar(
                    x=value_counts.index, 
                    y=value_counts.values, 
                    title=title, 
                    height=500,
                    color=value_counts.values,
                    color_continuous_scale='Blues'
                )
                
                fig.update_layout(
                    xaxis_tickangle=-45,
                    showlegend=False,
                    xaxis_title=col.replace('_', ' ').title(),
                    yaxis_title="Count"
                )
                
                fig.update_traces(
                    texttemplate='%{y}',
                    textposition='outside',
                    hovertemplate='<b>%{x}</b><br>Count: %{y}<extra></extra>'
                )
                
                return fig
                
        except Exception as e:
            print(f"❌ Bar chart creation error: {str(e)}")
            return None
    
    def _create_advanced_pie_chart(self, df: pd.DataFrame, title: str) -> go.Figure:
        """Create advanced pie chart with proper labels"""
        try:
            print(f"🥧 Creating pie chart with data: {df.shape}")
            print(f"📊 Columns: {list(df.columns)}")
            print(f"📊 Data sample: {df.head().to_dict()}")
            
            # Handle different data structures
            if len(df.columns) == 1:
                # Single column - treat as values, use index as labels
                col = df.columns[0]
                if df.index.name or not all(isinstance(x, int) for x in df.index):
                    # Index has meaningful labels
                    labels = df.index.tolist()
                    values = df[col].tolist()
                else:
                    # No meaningful labels, create value counts
                    value_counts = df[col].value_counts()
                    labels = value_counts.index.tolist()
                    values = value_counts.values.tolist()
                
                # Limit to top 10 for readability
                if len(labels) > 10:
                    labels = labels[:10]
                    values = values[:10]
                
                fig = px.pie(
                    names=labels,
                    values=values,
                    title=title,
                    height=500
                )
                
            elif len(df.columns) >= 2:
                # Multiple columns - find labels and values
                labels_col = None
                values_col = None
                
                # Look for common label column patterns
                for col in df.columns:
                    if col.lower() in ['name', 'category', 'brand', 'type', 'label', '_id']:
                        labels_col = col
                        break
                
                # Look for numeric values column
                for col in df.columns:
                    if col != labels_col and pd.api.types.is_numeric_dtype(df[col]):
                        values_col = col
                        break
                
                # Fallback to first two columns
                if labels_col is None:
                    labels_col = df.columns[0]
                if values_col is None:
                    values_col = df.columns[1] if len(df.columns) > 1 else df.columns[0]
                
                # Ensure values are numeric
                if not pd.api.types.is_numeric_dtype(df[values_col]):
                    print(f"⚠️ Converting {values_col} to numeric")
                    df[values_col] = pd.to_numeric(df[values_col], errors='coerce')
                    df = df.dropna(subset=[values_col])
                
                # Remove zero values
                df = df[df[values_col] > 0]
                
                if df.empty:
                    print("❌ No valid data for pie chart after cleaning")
                    return None
                
                # Limit to top 10 for readability
                if len(df) > 10:
                    df = df.nlargest(10, values_col)
                
                # Handle complex labels (like nested objects)
                labels = []
                for label in df[labels_col]:
                    if isinstance(label, dict):
                        # Extract meaningful part from dict
                        if 'name' in label:
                            labels.append(str(label['name']))
                        elif 'category' in label:
                            labels.append(str(label['category']))
                        else:
                            labels.append(str(list(label.values())[0]))
                    else:
                        labels.append(str(label))
                
                fig = px.pie(
                    names=labels,
                    values=df[values_col],
                    title=title,
                    height=500
                )
            
            else:
                print("❌ No data available for pie chart")
                return None
            
            # Improve layout
            fig.update_traces(
                textposition='inside',
                textinfo='percent+label',
                hovertemplate='<b>%{label}</b><br>Value: %{value}<br>Percentage: %{percent}<extra></extra>'
            )
            
            fig.update_layout(
                showlegend=True,
                legend=dict(
                    orientation="v",
                    yanchor="top",
                    y=1,
                    xanchor="left",
                    x=1.01
                ),
                font=dict(size=12)
            )
            
            print("✅ Pie chart created successfully")
            return fig
            
        except Exception as e:
            print(f"❌ Pie chart creation error: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
    
    def _create_advanced_line_chart(self, df: pd.DataFrame, title: str) -> go.Figure:
        """Create advanced line chart"""
        if len(df.columns) >= 2:
            x_col = df.columns[0]
            y_col = df.columns[1]
            
            fig = px.line(df, x=x_col, y=y_col, title=title, height=500)
            return fig
        else:
            # Single column - create index-based line chart
            col = df.columns[0]
            fig = px.line(x=df.index, y=df[col], title=title, height=500)
            fig.update_xaxes(title="Index")
            fig.update_yaxes(title=col)
            return fig
    
    def _create_advanced_scatter_plot(self, df: pd.DataFrame, title: str) -> go.Figure:
        """Create advanced scatter plot"""
        if len(df.columns) >= 2:
            x_col = df.columns[0]
            y_col = df.columns[1]
            
            color_col = df.columns[2] if len(df.columns) > 2 else None
            
            fig = px.scatter(df, x=x_col, y=y_col, color=color_col, 
                           title=title, height=500)
            return fig
        else:
            # Single column - create scatter against index
            col = df.columns[0]
            fig = px.scatter(x=df.index, y=df[col], title=title, height=500)
            fig.update_xaxes(title="Index")
            fig.update_yaxes(title=col)
            return fig

# ==================== SMART QUERY GENERATOR ====================
class SmartQueryGenerator:
    """Generate intelligent MongoDB queries"""
    
    def __init__(self, mongo_system: AutonomousMongoSystem):
        self.mongo_system = mongo_system
    
    
    def generate_query(self, user_query: str, chat_history: list = None) -> str:
        """Generate MongoDB query using AI with improved collection name handling"""
        if chat_history is None:
            chat_history = []
        
        # Extract any error feedback for retries
        error_feedback = ""
        if "Error feedback:" in user_query:
            parts = user_query.split("Error feedback:", 1)
            user_query = parts[0].strip()
            error_feedback = parts[1].strip()
        
        template = """
        You are an expert MongoDB query generator. Based on the database schema and user question, 
        generate a single-line PyMongo query that answers their question.

        <SCHEMA>{schema}</SCHEMA>

        {error_feedback}

        CRITICAL RULES:
        1. Generate ONLY single-line PyMongo code
        2. Use 'db' for database operations, 'client' for admin operations
        3. NO imports, NO multi-line code, NO explanations
        4. Available functions: list, json, len, str, int, float, dict, set, tuple, range, random, uuid, datetime
        5. For visualization requests, return aggregated data suitable for charts
        6. Use proper MongoDB methods: find(), aggregate(), count_documents(), insert_many(), update_many(), delete_many()
        7. For updates: db.collection.update_many({{}}, {{"$set": {{"field": value}}}})
        8. For inserts: db.collection.insert_many([{{"field": "value"}}])
        9. Handle case-sensitive method names correctly
        
        IMPORTANT FOR COLLECTION NAMES:
        - For collection names with hyphens, spaces, or special characters, ALWAYS use bracket notation: db["collection-name"]
        - Never use dot notation (db.collection-name) for collections with hyphens or special characters
        - Example correct usage: db["comics-character"].find()
        - Example incorrect usage: db.comics-character.find()

        VISUALIZATION QUERY EXAMPLES:
        User: "pie chart of products by category"
        Query: list(db["products"].aggregate([{{"$group": {{"_id": "$category", "count": {{"$sum": 1}}}}}}]))

        User: "bar chart of products by price"
        Query: list(db["products"].aggregate([{{"$group": {{"_id": "$product_name", "price": {{"$first": "$product_price"}}}}}}]))

        REGULAR QUERY EXAMPLES:
        User: "show me all products"
        Query: list(db["products"].find())

        User: "count products by category"
        Query: list(db["products"].aggregate([{{"$group": {{"_id": "$category", "count": {{"$sum": 1}}}}}}]))

        User: "show me comics-character collection"
        Query: list(db["comics-character"].find())

        Question: {question}
        PyMongo Query:
        """
        
        prompt = ChatPromptTemplate.from_template(template)
        llm = ChatAnthropic(model="claude-3-sonnet-20240229", temperature=0)
        
        chain = prompt | llm | StrOutputParser()
        
        return chain.invoke({
            "question": user_query,
            "schema": self.mongo_system.get_schema_info(),
            "error_feedback": error_feedback
        })
   
    def generate_response(self, user_query: str, query_result: str) -> str:
        """Generate natural language response with proper markdown"""
        template = """
        You are a helpful AI assistant analyzing MongoDB data. Provide a natural, conversational response with proper markdown formatting.

        User's Question: {question}
        Query Results: {results}
        
        RESPONSE GUIDELINES:
        1. Be conversational and direct - answer what they asked
        2. Keep it SHORT and FRIENDLY (max 2-3 sentences for simple queries)
        3. Use **proper markdown formatting**:
           - **bold** for important numbers and key terms
           - `code` for field names and technical terms
           - - bullet points for lists (when needed)
           - > quotes for important insights
        4. For data listings: mention count and highlight interesting items
        5. For charts/visualization: acknowledge the chart was created
        6. For operations (insert/update/delete): confirm what was done
        7. For analysis queries: give key insights naturally
        8. Always end with a helpful suggestion using 💡
        9. Be friendly but concise - like talking to a colleague

        EXAMPLES:
        
        User: "list all products"
        Response: "Found **25 products** in your database! You have tech items like smartphones and laptops, plus **20 herbal products** from Himalaya brand. 💡 Try `group products by category` to see the breakdown!"

        User: "count users"
        Response: "You have **89 users** registered in your system. 💡 Want to see `user registration trends over time`?"

        User: "create a chart of sales"
        Response: "I've created a **bar chart** showing your sales data! The visualization reveals clear patterns in your business performance. 💡 Try `show top selling products` for more insights!"

        User: "insert sample data"
        Response: "Successfully inserted **10 sample records** into your `products` collection! 💡 Use `show recent products` to see what was added."

        User: "products without price"
        Response: "Found **10 products** missing price information - mostly Himalaya herbal products like `Ashwagandha` and `Triphala`. 💡 Try `update products set price for missing items`!"

        User: "top 3 products by price"
        Response: "Here are your **top 3 most expensive products**:
        
        - **Samsung Galaxy S24** - $999
        - **Google Pixel 7 Pro** - $999  
        - **OnePlus 10 Pro** - $999
        
        💡 Try `create chart of product prices` to visualize the full range!"

        Your Response (use proper markdown):
        """
        
        prompt = ChatPromptTemplate.from_template(template)
        llm = ChatAnthropic(model="claude-3-sonnet-20240229", temperature=0.3)
        
        chain = prompt | llm | StrOutputParser()
        
        return chain.invoke({
            "question": user_query,
            "results": query_result
        })




# ==================== QUERY LOGGER SYSTEM ====================

class QueryLogger:
    """Logger for MongoDB queries with stats tracking and analysis"""
    
    def __init__(self):
        """Initialize the logger with storage for stats"""
        self.logs_dir = "query_logs"
        self.today_logs = []
        self.total_queries = 0
        self.successful_queries = 0
        self.total_execution_time = 0
        self.visualization_count = 0
        
        # Ensure logs directory exists
        self._ensure_logs_dir()
    
    def _ensure_logs_dir(self):
        """Ensure the logs directory exists"""
        try:
            from pathlib import Path
            Path(self.logs_dir).mkdir(exist_ok=True)
            print(f"✅ Logs directory ready: {self.logs_dir}")
        except Exception as e:
            print(f"⚠️ Warning: Could not create logs directory: {str(e)}")
    
    def log_query(self, user_query: str, mongodb_query: str, response: str, 
                 success: bool, execution_time: float, retry_count: int = 0,
                 error_reason: str = None, has_visualization: bool = False):
        """Log a query with all relevant information"""
        try:
            # Create log entry
            from datetime import datetime
            import json
            import os
            
            timestamp = datetime.now().isoformat()
            today = datetime.now().strftime("%Y-%m-%d")
            
            log_entry = {
                "timestamp": timestamp,
                "user_query": user_query,
                "mongodb_query": mongodb_query,
                "success": success,
                "execution_time": execution_time,
                "retry_count": retry_count,
                "has_visualization": has_visualization,
                "error_reason": error_reason,
                "response": response[:500]  # Truncated response
            }
            
            # Update stats
            self.total_queries += 1
            if success:
                self.successful_queries += 1
            self.total_execution_time += execution_time
            if has_visualization:
                self.visualization_count += 1
            
            # Add to today's logs
            self.today_logs.append(log_entry)
            
            # Write to daily log file
            log_file = os.path.join(self.logs_dir, f"{today}.json")
            
            # Load existing logs
            existing_logs = []
            if os.path.exists(log_file):
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        existing_logs = json.load(f)
                except Exception as e:
                    print(f"⚠️ Warning: Could not read existing log file: {str(e)}")
            
            # Append new log
            existing_logs.append(log_entry)
            
            # Write updated logs
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(existing_logs, f, ensure_ascii=False, indent=2)
            
            print(f"📝 Query logged successfully: {log_file}")
            return True
            
        except Exception as e:
            print(f"❌ Logging error: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
    
    def get_today_stats(self):
        """Get today's query statistics"""
        total = len(self.today_logs)
        success = sum(1 for log in self.today_logs if log.get('success', False))
        
        # Calculate average execution time
        if total > 0:
            avg_time = sum(log.get('execution_time', 0) for log in self.today_logs) / total
            avg_time = round(avg_time, 2)
        else:
            avg_time = 0
        
        # Calculate visualization percentage
        viz_count = sum(1 for log in self.today_logs if log.get('has_visualization', False))
        viz_percent = (viz_count / total * 100) if total > 0 else 0
        
        # Calculate retry statistics
        retries = sum(log.get('retry_count', 0) for log in self.today_logs)
        avg_retries = round(retries / total, 1) if total > 0 else 0
        
        return {
            "total": total,
            "success": success,
            "success_rate": f"{success/total:.1%}" if total > 0 else "0%",
            "avg_time": avg_time,
            "viz_count": viz_count,
            "viz_percent": f"{viz_percent:.1f}%",
            "retries": retries,
            "avg_retries": avg_retries
        }
    
    def get_recent_logs(self, limit=5):
        """Get the most recent log entries"""
        # Sort by timestamp in descending order and take the most recent 'limit' entries
        sorted_logs = sorted(self.today_logs, 
                            key=lambda x: x.get('timestamp', ''), 
                            reverse=True)
        return sorted_logs[:limit]
    
    def get_error_summary(self):
        """Get summary of errors for debugging"""
        error_logs = [log for log in self.today_logs if not log.get('success', False)]
        
        # Group by error reason
        error_counts = {}
        for log in error_logs:
            reason = log.get('error_reason', 'Unknown error')
            # Truncate very long error messages
            if isinstance(reason, str) and len(reason) > 100:
                reason = reason[:100] + "..."
            
            if reason in error_counts:
                error_counts[reason] += 1
            else:
                error_counts[reason] = 1
        
        # Sort by frequency
        sorted_errors = sorted(error_counts.items(), key=lambda x: x[1], reverse=True)
        
        return {
            "total_errors": len(error_logs),
            "error_types": dict(sorted_errors),
            "error_rate": f"{len(error_logs)/self.total_queries:.1%}" if self.total_queries > 0 else "0%"
        }
    
    def export_logs(self, format='json'):
        """Export logs in different formats"""
        import json
        from datetime import datetime
        import os
        
        today = datetime.now().strftime("%Y-%m-%d")
        export_dir = os.path.join(self.logs_dir, "exports")
        os.makedirs(export_dir, exist_ok=True)
        
        if format.lower() == 'json':
            export_path = os.path.join(export_dir, f"{today}_export.json")
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(self.today_logs, f, ensure_ascii=False, indent=2)
            return export_path
        
        elif format.lower() == 'csv':
            import csv
            export_path = os.path.join(export_dir, f"{today}_export.csv")
            
            # Define CSV fields
            fields = ['timestamp', 'user_query', 'mongodb_query', 'success', 
                     'execution_time', 'retry_count', 'has_visualization', 'error_reason']
            
            with open(export_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fields)
                writer.writeheader()
                
                # Write logs with only the selected fields
                for log in self.today_logs:
                    row = {field: log.get(field, '') for field in fields}
                    writer.writerow(row)
            
            return export_path
        
        else:
            return None
    
    def analyze_performance(self):
        """Analyze performance trends"""
        if not self.today_logs:
            return {"message": "No logs available for analysis"}
        
        # Calculate performance metrics over time
        from datetime import datetime
        
        # Convert timestamps to datetime objects
        for log in self.today_logs:
            log['datetime'] = datetime.fromisoformat(log['timestamp'])
        
        # Sort logs by timestamp
        sorted_logs = sorted(self.today_logs, key=lambda x: x['datetime'])
        
        # Group by hour
        hourly_stats = {}
        for log in sorted_logs:
            hour = log['datetime'].strftime('%H')
            
            if hour not in hourly_stats:
                hourly_stats[hour] = {
                    'total': 0,
                    'success': 0,
                    'total_time': 0,
                    'visualizations': 0
                }
            
            hourly_stats[hour]['total'] += 1
            if log.get('success', False):
                hourly_stats[hour]['success'] += 1
            hourly_stats[hour]['total_time'] += log.get('execution_time', 0)
            if log.get('has_visualization', False):
                hourly_stats[hour]['visualizations'] += 1
        
        # Calculate averages
        for hour, stats in hourly_stats.items():
            if stats['total'] > 0:
                stats['avg_time'] = round(stats['total_time'] / stats['total'], 2)
                stats['success_rate'] = round(stats['success'] / stats['total'] * 100, 1)
            else:
                stats['avg_time'] = 0
                stats['success_rate'] = 0
        
        return {
            'hourly_stats': hourly_stats,
            'total_queries': self.total_queries,
            'overall_success_rate': round(self.successful_queries / self.total_queries * 100, 1) if self.total_queries > 0 else 0
        }



# ==================== PERFORMANCE OPTIMIZATION ====================
class PerformanceOptimizer:
    """Optimize MongoDB query performance for large datasets"""
    
    def __init__(self):
        """Initialize the performance optimizer"""
        self.optimization_threshold = 1000  # Optimize queries for collections larger than this
        self.max_results = 100  # Maximum results to return for large datasets
    
    def get_collection_size_estimate(self, collection) -> int:
        """Get an estimate of collection size"""
        try:
            # Try to get a quick estimate
            return collection.estimated_document_count()
        except Exception:
            try:
                # Fallback to count_documents (slower but more reliable)
                return collection.count_documents({})
            except Exception as e:
                print(f"⚠️ Could not estimate collection size: {str(e)}")
                return 0
    
    def optimize_query(self, query: str, collection_size: int) -> str:
        """Optimize MongoDB query for better performance"""
        if collection_size <= self.optimization_threshold:
            # No optimization needed for small collections
            return query
        
        print(f"🔍 Optimizing query for large collection: {collection_size} documents")
        
        # Check if this is a find query
        if 'find(' in query and '.limit(' not in query:
            # Add limit for find queries
            query = query.replace('find()', f'find().limit({self.max_results})')
            query = query.replace('find({})', f'find({{}}).limit({self.max_results})')
            
            # For more complex find queries, add the limit at the end
            if 'find(' in query and ')' in query and '.limit(' not in query:
                last_paren_index = query.rindex(')')
                query = query[:last_paren_index] + f').limit({self.max_results}' + query[last_paren_index:]
        
        # Check if this is an aggregation query
        if 'aggregate(' in query:
            # Check if it already has a $limit stage
            if '$limit' not in query:
                # Add $limit stage to aggregation pipeline
                if '])))' in query:
                    # Add limit before final closing brackets
                    query = query.replace('])))', f', {{"$limit": {self.max_results}}}])))') 
                elif ']))' in query:
                    # Add limit before final closing brackets
                    query = query.replace('])))', f', {{"$limit": {self.max_results}}}])))')
        
        print(f"✅ Optimized query: {query}")
        return query
    
    
# ==================== MAIN AUTONOMOUS SYSTEM ====================
class AutonomousMongoDBChat:
    """Main autonomous MongoDB chat system with logging and performance optimization"""
    
    def __init__(self):
        self.mongo_system = AutonomousMongoSystem()  # No longer auto-connects
        self.query_generator = SmartQueryGenerator(self.mongo_system)
        self.viz_detector = SmartVisualizationDetector()
        self.viz_generator = AdvancedVisualizationGenerator()
        self.logger = QueryLogger()
        self.performance_optimizer = PerformanceOptimizer()
        self.max_retries = 3
    
    def process_query(self, user_query: str, chat_history: list = None) -> Dict[str, Any]:
        """Process user query with intelligent error handling and retries"""
        start_time = time.time()
        
        result = {
            "success": False,
            "response": "",
            "visualization": None,
            "execution_time": 0,
            "generated_query": "",
            "raw_data": "",
            "retry_count": 0,
            "error_reason": None
        }
        
        print(f"\n{'='*100}")
        print(f"🚀 AUTONOMOUS MONGODB CHAT SYSTEM")
        print(f"{'='*100}")
        print(f"🎯 USER QUERY: {user_query}")
        print(f"🔗 CONNECTION: {'Live Database' if self.mongo_system.is_connected else 'Not Connected'}")
        print(f"📊 VISUALIZATION: {'Available' if VISUALIZATION_AVAILABLE else 'Disabled'}")
        print(f"{'='*100}")
        
        mongodb_query = ""
        previous_errors = []
        
        try:
            # Generate and execute query with adaptive retries
            for attempt in range(self.max_retries):
                try:
                    print(f"\n🔄 Attempt {attempt + 1}/{self.max_retries}")
                    
                    # For first attempt, just generate from user query
                    if attempt == 0:
                        generated_query = self.query_generator.generate_query(user_query, chat_history)
                    else:
                        # For subsequent attempts, include error information to help AI fix the query
                        error_context = f"""
                        The previous query failed with error: {previous_errors[-1]}
                        
                        Previous attempted query: {mongodb_query}
                        
                        Please fix the query considering the error. Common issues include:
                        1. Collection names with hyphens should use bracket notation: db["collection-name"] not db.collection-name
                        2. Check field names for typos
                        3. Ensure query syntax is correct for the MongoDB version
                        4. Check if fields exist in the collection
                        
                        Generate a corrected query that resolves the error.
                        """
                        
                        # Combine original query with error feedback
                        retry_prompt = f"{user_query}\n\nError feedback: {error_context}"
                        generated_query = self.query_generator.generate_query(retry_prompt, chat_history)
                    
                    mongodb_query = generated_query
                    
                    # Performance optimization for large datasets
                    if self.mongo_system.is_connected:
                        # Get collection size estimate for optimization
                        collection_size = 0
                        if 'db.' in generated_query or 'db[' in generated_query:
                            try:
                                # Extract collection name handling both dot notation and bracket notation
                                if 'db.' in generated_query:
                                    collection_match = re.search(r'db\.([a-zA-Z0-9_]+)', generated_query)
                                    if collection_match:
                                        collection_name = collection_match.group(1)
                                elif 'db[' in generated_query:
                                    collection_match = re.search(r'db\[[\'\"]([^\'"]+)[\'\"]\]', generated_query)
                                    if collection_match:
                                        collection_name = collection_match.group(1)
                                
                                if collection_match:
                                    collection = self.mongo_system.database[collection_name]
                                    collection_size = self.performance_optimizer.get_collection_size_estimate(collection)
                                    print(f"📊 Collection size estimate: {collection_size}")
                            except Exception as e:
                                print(f"⚠️ Collection size estimation failed: {str(e)}")
                        
                        # Optimize query if needed
                        if collection_size > 1000:
                            optimized_query = self.performance_optimizer.optimize_query(generated_query, collection_size)
                            if optimized_query != generated_query:
                                generated_query = optimized_query
                                mongodb_query = optimized_query
                    
                    result["generated_query"] = generated_query
                    
                    print(f"🤖 Generated Query: {generated_query}")
                    
                    # Execute query
                    query_result = self.mongo_system.execute_query(generated_query)
                    result["raw_data"] = query_result
                    
                    # Check for errors in result
                    if "Query Error:" in str(query_result):
                        error_msg = str(query_result)
                        previous_errors.append(error_msg)
                        raise Exception(error_msg)
                    
                    print(f"✅ Query executed successfully")
                    
                    # Generate natural language response
                    nl_response = self.query_generator.generate_response(user_query, query_result)
                    result["response"] = nl_response
                    result["success"] = True
                    result["retry_count"] = attempt
                    
                    print(f"💬 Generated response: {nl_response[:100]}...")
                    
                    # Check if visualization is needed
                    if self.viz_detector.needs_visualization(user_query):
                        print(f"🎨 Creating visualization...")
                        viz_data = self._prepare_visualization_data(query_result)
                        
                        if viz_data:
                            chart_type = self._determine_chart_type(user_query, viz_data)
                            title = self._generate_chart_title(user_query)
                            
                            fig = self.viz_generator.create_visualization(viz_data, chart_type, title)
                            result["visualization"] = fig
                            
                            if fig:
                                result["response"] += "\n\n📊 **Chart created successfully!**"
                                print(f"✅ Visualization created successfully")
                            else:
                                print(f"❌ Visualization creation failed")
                    
                    break  # Success, exit retry loop
                    
                except Exception as e:
                    error_msg = str(e)
                    print(f"❌ Attempt {attempt + 1} failed: {error_msg}")
                    previous_errors.append(error_msg)
                    result["error_reason"] = error_msg
                    
                    if attempt == self.max_retries - 1:
                        # Final attempt failed - generate a helpful response with suggestions
                        result["response"] = self._generate_helpful_error_response(user_query, previous_errors, mongodb_query)
                        result["success"] = False
                        result["retry_count"] = attempt + 1
                        result["error_reason"] = error_msg
                    else:
                        # Wait before retry
                        time.sleep(1)
                        continue
            
        except Exception as e:
            error_msg = str(e)
            result["response"] = self._generate_helpful_error_response(user_query, [error_msg], mongodb_query)
            result["success"] = False
            result["error_reason"] = error_msg
            print(f"❌ Critical error: {error_msg}")
        
        finally:
            result["execution_time"] = time.time() - start_time
            
            # Log the query
            self.logger.log_query(
                user_query=user_query,
                mongodb_query=mongodb_query,
                response=result["response"],
                success=result["success"],
                error_reason=result["error_reason"],
                execution_time=result["execution_time"],
                retry_count=result["retry_count"],
                has_visualization=result["visualization"] is not None
            )
            
            print(f"\n📊 EXECUTION SUMMARY:")
            print(f"   ✅ Success: {result['success']}")
            print(f"   ⏱️ Time: {result['execution_time']:.2f}s")
            print(f"   🔄 Retries: {result['retry_count']}")
            print(f"   📈 Visualization: {'Created' if result['visualization'] else 'None'}")
            print(f"   📝 Logged: ✅")
            print(f"{'='*100}")
        
        return result
        
    def _generate_helpful_error_response(self, user_query: str, errors: List[str], last_query: str) -> str:
        """Generate a helpful response when queries fail, with suggestions for the user"""
        
        template = """
        You are an expert MongoDB assistant trying to help a user who has encountered an error.
        
        User's original question: {question}
        
        Attempted MongoDB query: {query}
        
        Error(s) encountered: {errors}
        
        Your task:
        1. Explain what went wrong in simple, friendly language
        2. Suggest possible fixes for the issue
        3. Ask clarifying questions if needed
        4. Provide alternative approaches if appropriate
        5. End with a friendly suggestion for what they might try next
        
        GUIDELINES:
        - Be conversational and helpful, not technical
        - Suggest 2-3 specific alternatives they could try
        - If collection names might be wrong, ask them about available collections
        - If field names might be wrong, ask for clarification
        - Format your response with markdown for readability
        - Make it feel like a conversation with a helpful expert
        - Keep it brief (max 4-5 sentences)
        - Use emoji 💡 for suggestions
        
        Your helpful response:
        """
        
        prompt = ChatPromptTemplate.from_template(template)
        llm = ChatAnthropic(model="claude-3-sonnet-20240229", temperature=0.3)
        
        chain = prompt | llm | StrOutputParser()
        
        return chain.invoke({
            "question": user_query,
            "query": last_query,
            "errors": "\n".join(errors)
        })
        
        
    def _prepare_visualization_data(self, query_result: str) -> List[Dict]:
        """Prepare data for visualization"""
        try:
            print(f"🔍 Preparing visualization data from: {query_result[:200]}...")
            
            # Parse JSON result
            if isinstance(query_result, str):
                if query_result.startswith('[') or query_result.startswith('{'):
                    data = json.loads(query_result)
                else:
                    print("❌ Result is not JSON format")
                    return []
            else:
                data = query_result
            
            # Ensure it's a list
            if isinstance(data, dict):
                data = [data]
            elif not isinstance(data, list):
                print("❌ Data is not a list or dict")
                return []
            
            print(f"📊 Raw data length: {len(data)}")
            
            # Clean the data
            cleaned_data = []
            for item in data:
                if isinstance(item, dict):
                    clean_item = {}
                    for key, value in item.items():
                        # Skip MongoDB internal fields
                        if key.startswith('_'):
                            continue
                        
                        # Handle different value types
                        if isinstance(value, (str, int, float, bool)):
                            clean_item[key] = value
                        elif isinstance(value, dict):
                            clean_item[key] = str(value)
                        elif isinstance(value, list):
                            clean_item[key] = len(value)
                        else:
                            clean_item[key] = str(value)
                    
                    if clean_item:
                        cleaned_data.append(clean_item)
            
            print(f"✅ Cleaned data: {len(cleaned_data)} records")
            if cleaned_data:
                print(f"📝 Sample record: {cleaned_data[0]}")
            
            return cleaned_data
            
        except Exception as e:
            print(f"❌ Data preparation error: {str(e)}")
            return []
    
    def _determine_chart_type(self, user_query: str, data: List[Dict]) -> str:
        """Intelligent chart type determination based on data analysis"""
        
        # If explicitly requested in query, honor that
        query_lower = user_query.lower()
        if any(word in query_lower for word in ['pie', 'donut']):
            return "pie"
        if any(word in query_lower for word in ['line', 'trend', 'over time']):
            return "line"
        if any(word in query_lower for word in ['scatter', 'correlation']):
            return "scatter"
        
        # Analyze data structure for intelligent defaults
        if not data:
            return "bar"  # Default with no data
        
        # Convert to DataFrame for analysis
        df = pd.DataFrame(data)
        
        # Look for temporal patterns
        date_columns = [col for col in df.columns if 'date' in str(col).lower() 
                    or 'time' in str(col).lower() 
                    or 'year' in str(col).lower()]
        
        if date_columns and len(df) > 3:
            return "line"  # Time series data
        
        # Look for categorical distributions
        categorical_columns = [col for col in df.columns 
                            if df[col].nunique() < 10 and df[col].nunique() > 1]
        
        if categorical_columns and len(categorical_columns) >= 2:
            return "heatmap"  # Multiple categories
        
        if categorical_columns and len(df) > 5 and df[categorical_columns[0]].nunique() <= 6:
            return "pie"  # Good categorical distribution for pie
            
        # For numeric columns, check distribution
        numeric_columns = df.select_dtypes(include=['number']).columns
        
        if len(numeric_columns) >= 2:
            # Check correlation between first two numeric columns
            if abs(df[numeric_columns[0]].corr(df[numeric_columns[1]])) > 0.3:
                return "scatter"  # Correlated numerics
        
        # Default to bar for most other cases
        return "bar"
    def _generate_chart_title(self, user_query: str) -> str:
        """Generate appropriate chart title"""
        query_lower = user_query.lower()
        
        # Common chart title patterns
        if 'by' in query_lower:
            # Extract pattern like "products by category"
            parts = query_lower.split('by')
            if len(parts) >= 2:
                main_part = parts[0].strip()
                group_part = parts[1].strip()
                
                # Clean up the parts
                main_part = main_part.replace('show', '').replace('get', '').replace('create', '').replace('chart', '').replace('pie', '').replace('bar', '').strip()
                group_part = group_part.replace('their', '').replace('the', '').strip()
                
                return f"{main_part.title()} by {group_part.title()}"
        
        # Extract meaningful words
        words = user_query.split()
        meaningful_words = []
        
        skip_words = ['show', 'me', 'the', 'a', 'an', 'get', 'find', 'create', 'make', 'chart', 'pie', 'bar', 'line', 'of', 'with', 'please', 'pls']
        
        for word in words:
            if word.lower() not in skip_words:
                meaningful_words.append(word)
        
        if meaningful_words:
            title = ' '.join(meaningful_words[:4]).title()
            return title
        
        return "Data Analysis"

# ==================== STREAMLIT INTERFACE ====================

def create_autonomous_interface():
    """Create fully autonomous Streamlit interface with explicit connection required"""
    
    # Initialize the autonomous system
    if "autonomous_mongo" not in st.session_state:
        st.session_state.autonomous_mongo = AutonomousMongoDBChat()
    
    # Initialize chat history
    if "chat_history" not in st.session_state:
        st.session_state.chat_history = [
            AIMessage(content="🚀 **Welcome to the MongoDB Chat!**\n\nI'm your AI-powered MongoDB assistant. Please connect to your MongoDB database using the sidebar to get started.\n\n💡 **Try asking once connected:**\n- \"What data do you have?\"\n- \"Show me my products\"\n- \"Create a chart of sales by month\"\n- \"How many customers do I have?\"\n- \"Insert sample data\"\n- \"Update my records\"\n\nLet's explore your data together! 🎯")
        ]
    
    # Load environment
    load_dotenv()
    
    # Page configuration
    st.set_page_config(
        page_title="MongoDB Chat", 
        page_icon="🚀", 
        layout="wide",
        initial_sidebar_state="expanded"  # Start with sidebar open for connection
    )
    
    # Main header
    st.title("🚀 Datapilot - MongoDB Chat")
    st.markdown("*Your AI-powered MongoDB assistant*")
    
    # Connection status
    mongo_system = st.session_state.autonomous_mongo.mongo_system
    if mongo_system.is_connected:
        st.success(f"🟢 **Connected to: {mongo_system.current_db_name}** | {len(mongo_system.database.list_collection_names())} collections | Use sidebar to switch databases")
    else:
        st.warning("⚠️ **Not Connected** - Please use the sidebar to connect to your MongoDB database")
    
    # Quick stats with logging info
    if mongo_system.is_connected:
        col1, col2, col3, col4, col5 = st.columns(5)
        with col1:
            st.metric("Total Queries", len([msg for msg in st.session_state.chat_history if isinstance(msg, HumanMessage)]))
        with col2:
            st.metric("Connection", "Active")
        with col3:
            st.metric("Visualization", "Enabled" if VISUALIZATION_AVAILABLE else "Disabled")
        with col4:
            st.metric("Database", mongo_system.current_db_name)
        with col5:
            # Today's stats
            if hasattr(st.session_state.autonomous_mongo, 'logger'):
                today_stats = st.session_state.autonomous_mongo.logger.get_today_stats()
                st.metric("Today's Success Rate", f"{today_stats['success']}/{today_stats['total']}")
            else:
                st.metric("Logging", "Active")
    
    # Chat interface
    st.markdown("---")
    
    # Display chat history
    for message in st.session_state.chat_history:
        if isinstance(message, AIMessage):
            with st.chat_message("assistant", avatar="🤖"):
                st.markdown(message.content)
        elif isinstance(message, HumanMessage):
            with st.chat_message("user", avatar="👤"):
                st.markdown(message.content)
    
    # Chat input - only enable if connected
    if mongo_system.is_connected:
        user_query = st.chat_input("Ask me anything about your MongoDB data...")
    else:
        user_query = st.chat_input("Connect to your database in the sidebar to start...", disabled=True)
        # Show connection instructions prominently
        st.info("👈 Please use the sidebar to connect to your MongoDB database first.")
    
    if user_query and mongo_system.is_connected:
        # Add user message
        st.session_state.chat_history.append(HumanMessage(content=user_query))
        
        with st.chat_message("user", avatar="👤"):
            st.markdown(user_query)
        
        # Process query
        with st.chat_message("assistant", avatar="🤖"):
            with st.spinner("🔍 Analyzing your request..."):
                result = st.session_state.autonomous_mongo.process_query(
                    user_query, 
                    st.session_state.chat_history
                )
            
            # Display response
            st.markdown(result["response"])
            
            # Display visualization if available
            if result["visualization"]:
                st.plotly_chart(result["visualization"], use_container_width=True)
            
            # Show execution info
            col1, col2, col3 = st.columns(3)
            with col1:
                st.caption(f"⏱️ {result['execution_time']:.2f}s")
            with col2:
                st.caption(f"{'✅' if result['success'] else '❌'} {'Success' if result['success'] else 'Failed'}")
            with col3:
                if result['retry_count'] > 0:
                    st.caption(f"🔄 {result['retry_count']} retries")
            
            # Add expandable debug info
            if result['generated_query']:
                with st.expander("🔍 Query Details"):
                    st.code(result['generated_query'], language='python')
            
            response_content = result["response"]
        
        # Add assistant response to history
        st.session_state.chat_history.append(AIMessage(content=response_content))
    
    # Sidebar with connection and quick actions
    with st.sidebar:
        st.header("🔗 MongoDB Connection")
        
        # Connection status
        mongo_system = st.session_state.autonomous_mongo.mongo_system
        if mongo_system.is_connected:
            st.success(f"🟢 **Connected to: {mongo_system.current_db_name}**")
            st.caption(f"📊 {len(mongo_system.database.list_collection_names())} collections")
            
            # Database switcher
            available_dbs = mongo_system.get_available_databases()
            if len(available_dbs) > 1:
                st.markdown("**Switch Database:**")
                selected_db = st.selectbox(
                    "Available Databases",
                    options=available_dbs,
                    index=available_dbs.index(mongo_system.current_db_name) if mongo_system.current_db_name in available_dbs else 0,
                    key="db_selector"
                )
                
                if selected_db != mongo_system.current_db_name:
                    if st.button("🔄 Switch Database", use_container_width=True):
                        if mongo_system.switch_database(selected_db):
                            st.success(f"✅ Switched to {selected_db}")
                            st.rerun()
                        else:
                            st.error("❌ Failed to switch database")
        else:
            st.warning("⚠️ Not connected")
        
        # MongoDB connection
        st.markdown("### Connect to MongoDB")
        st.markdown("*Enter connection details to connect to your database*")
        
        st.markdown("**Connection Examples:**")
        st.markdown("- Local: `mongodb://localhost:27017/`")
        st.markdown("- Atlas: `mongodb+srv://username:<EMAIL>/`")
        st.markdown("- Replica set: `mongodb://host1:27017,host2:27017/?replicaSet=rs0`")
        st.markdown("- With auth: `********************************:port/`")
        
        connection_string = st.text_input(
            "Connection String",
            value="",
            placeholder="mongodb://localhost:27017/"
        )
        
        database_name = st.text_input(
            "Database Name",
            value="",
            placeholder="your_database_name"
        )
        
        if st.button("🔗 Connect", use_container_width=True):
            if connection_string and database_name:
                with st.spinner("🔄 Connecting..."):
                    if st.session_state.autonomous_mongo.mongo_system.connect(connection_string, database_name):
                        st.success("✅ Connected successfully!")
                        st.rerun()
                    else:
                        st.error("❌ Connection failed. Check your connection string and database name.")
            else:
                st.error("Please enter both connection string and database name")
        
        # Disconnect option
        if mongo_system.is_connected:
            if st.button("❌ Disconnect", use_container_width=True):
                # Reset the MongoDB system
                st.session_state.autonomous_mongo = AutonomousMongoDBChat()
                st.success("✅ Disconnected!")
                st.rerun()
        
        st.markdown("---")
        
        # Clear chat
        if st.button("🗑️ Clear Chat", use_container_width=True):
            st.session_state.chat_history = [st.session_state.chat_history[0]]  # Keep welcome message
            st.rerun()
        
        # Show today's query logs
        if hasattr(st.session_state.autonomous_mongo, 'logger') and mongo_system.is_connected:
            today_stats = st.session_state.autonomous_mongo.logger.get_today_stats()
            if today_stats['total'] > 0:
                st.markdown("---")
                st.markdown("📊 **Today's Stats**")
                st.markdown(f"- **Total Queries**: {today_stats['total']}")
                st.markdown(f"- **Success Rate**: {today_stats['success']}/{today_stats['total']}")
                st.markdown(f"- **Avg Time**: {today_stats['avg_time']}s")
                
                # Show log file location
                today = datetime.now().strftime("%Y-%m-%d")
                log_path = f"query_logs/{today}.json"
                if os.path.exists(log_path):
                    st.caption(f"📝 Logs: `{log_path}`")
# ==================== MAIN EXECUTION ====================
if __name__ == "__main__":
    create_autonomous_interface()