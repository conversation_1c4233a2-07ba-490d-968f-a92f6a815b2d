"""
Test Data Generator for MongoDB Collection Discovery POC
Creates sample data to test the intelligent discovery system
"""

import pymongo
from pymongo import MongoClient
import random
from datetime import datetime, timedelta
from typing import List, Dict
import json

class TestDataGenerator:
    """Generate test data for MongoDB collection discovery testing"""
    
    def __init__(self, connection_string: str = "mongodb://localhost:27017/"):
        self.connection_string = connection_string
        self.client = None
    
    def connect(self):
        """Connect to MongoDB"""
        try:
            self.client = MongoClient(self.connection_string)
            self.client.admin.command('ping')
            print("✅ Connected to MongoDB")
            return True
        except Exception as e:
            print(f"❌ Failed to connect: {str(e)}")
            return False
    
    def generate_ecommerce_data(self):
        """Generate comprehensive e-commerce test data"""
        if not self.client:
            print("❌ Not connected to MongoDB")
            return
        
        print("🏪 Generating E-commerce Test Data...")
        
        # Create databases
        ecommerce_db = self.client['ecommerce_main']
        customer_db = self.client['customer_management']
        order_db = self.client['order_processing']
        analytics_db = self.client['analytics_data']
        
        # Generate data for each database
        self._generate_product_data(ecommerce_db)
        self._generate_review_data(ecommerce_db)
        self._generate_customer_data(customer_db)
        self._generate_feedback_data(customer_db)
        self._generate_order_data(order_db)
        self._generate_support_data(order_db)
        self._generate_analytics_data(analytics_db)
        
        print("✅ E-commerce test data generated successfully!")
    
    def _generate_product_data(self, db):
        """Generate product catalog data"""
        print("   📦 Generating products...")
        
        categories = ['Electronics', 'Books', 'Clothing', 'Home & Garden', 'Sports', 'Beauty']
        brands = ['Apple', 'Samsung', 'Nike', 'Adidas', 'Sony', 'LG', 'Generic']
        
        products = []
        for i in range(200):
            product = {
                'product_id': f'PROD_{i+1:04d}',
                'name': f'Product {i+1}',
                'category': random.choice(categories),
                'brand': random.choice(brands),
                'price': round(random.uniform(10, 1000), 2),
                'description': f'High quality {random.choice(categories).lower()} product',
                'stock_quantity': random.randint(0, 100),
                'rating_average': round(random.uniform(1, 5), 1),
                'review_count': random.randint(0, 500),
                'created_date': datetime.now() - timedelta(days=random.randint(1, 365)),
                'is_active': random.choice([True, True, True, False])  # 75% active
            }
            products.append(product)
        
        db.products.drop()
        db.products.insert_many(products)
        print(f"      ✅ Created {len(products)} products")
    
    def _generate_review_data(self, db):
        """Generate customer reviews and ratings"""
        print("   ⭐ Generating reviews...")
        
        satisfaction_terms = [
            'excellent', 'great', 'good', 'satisfied', 'happy', 'pleased',
            'disappointed', 'poor', 'terrible', 'unsatisfied', 'bad'
        ]
        
        reviews = []
        for i in range(500):
            rating = random.randint(1, 5)
            sentiment = 'positive' if rating >= 4 else 'negative' if rating <= 2 else 'neutral'
            
            review = {
                'review_id': f'REV_{i+1:04d}',
                'product_id': f'PROD_{random.randint(1, 200):04d}',
                'customer_id': f'CUST_{random.randint(1, 100):04d}',
                'rating': rating,
                'review_text': f'This product is {random.choice(satisfaction_terms)}. ' + 
                              f'I am {sentiment} with my purchase.',
                'sentiment': sentiment,
                'satisfaction_score': rating * 2,  # Scale to 1-10
                'helpful_votes': random.randint(0, 50),
                'created_date': datetime.now() - timedelta(days=random.randint(1, 180)),
                'verified_purchase': random.choice([True, False])
            }
            reviews.append(review)
        
        db.customer_ratings.drop()
        db.customer_ratings.insert_many(reviews)
        print(f"      ✅ Created {len(reviews)} reviews")
    
    def _generate_customer_data(self, db):
        """Generate customer data"""
        print("   👥 Generating customers...")
        
        cities = ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia']
        age_groups = ['18-24', '25-34', '35-44', '45-54', '55-64', '65+']
        
        customers = []
        for i in range(100):
            customer = {
                'customer_id': f'CUST_{i+1:04d}',
                'name': f'Customer {i+1}',
                'email': f'customer{i+1}@email.com',
                'age': random.randint(18, 70),
                'age_group': random.choice(age_groups),
                'city': random.choice(cities),
                'registration_date': datetime.now() - timedelta(days=random.randint(1, 730)),
                'total_orders': random.randint(0, 50),
                'total_spent': round(random.uniform(0, 5000), 2),
                'loyalty_tier': random.choice(['Bronze', 'Silver', 'Gold', 'Platinum']),
                'is_active': random.choice([True, True, True, False])
            }
            customers.append(customer)
        
        db.users.drop()
        db.users.insert_many(customers)
        print(f"      ✅ Created {len(customers)} customers")
    
    def _generate_feedback_data(self, db):
        """Generate customer feedback and NPS data"""
        print("   📝 Generating feedback...")
        
        feedback_types = ['NPS Survey', 'Product Feedback', 'Service Feedback', 'General Feedback']
        
        feedback = []
        for i in range(150):
            nps_score = random.randint(0, 10)
            satisfaction_level = 'Promoter' if nps_score >= 9 else 'Detractor' if nps_score <= 6 else 'Passive'
            
            feedback_item = {
                'feedback_id': f'FB_{i+1:04d}',
                'customer_id': f'CUST_{random.randint(1, 100):04d}',
                'feedback_type': random.choice(feedback_types),
                'nps_score': nps_score,
                'satisfaction_score': random.randint(1, 10),
                'satisfaction_level': satisfaction_level,
                'feedback_text': f'Customer feedback about their experience. Satisfaction level: {satisfaction_level}',
                'survey_date': datetime.now() - timedelta(days=random.randint(1, 90)),
                'response_time': random.randint(1, 30)  # days to respond
            }
            feedback.append(feedback_item)
        
        db.nps_surveys.drop()
        db.nps_surveys.insert_many(feedback)
        print(f"      ✅ Created {len(feedback)} feedback entries")
    
    def _generate_order_data(self, db):
        """Generate order and transaction data"""
        print("   🛒 Generating orders...")
        
        statuses = ['completed', 'pending', 'shipped', 'delivered', 'cancelled', 'returned']
        
        orders = []
        returns = []
        
        for i in range(300):
            status = random.choice(statuses)
            order_date = datetime.now() - timedelta(days=random.randint(1, 365))
            
            order = {
                'order_id': f'ORD_{i+1:04d}',
                'customer_id': f'CUST_{random.randint(1, 100):04d}',
                'product_id': f'PROD_{random.randint(1, 200):04d}',
                'quantity': random.randint(1, 5),
                'unit_price': round(random.uniform(10, 500), 2),
                'total_amount': 0,  # Will calculate
                'order_date': order_date,
                'status': status,
                'fulfillment_time': random.randint(1, 14),  # days
                'shipping_cost': round(random.uniform(5, 25), 2)
            }
            order['total_amount'] = order['quantity'] * order['unit_price'] + order['shipping_cost']
            orders.append(order)
            
            # Generate some returns
            if status in ['completed', 'delivered'] and random.random() < 0.15:  # 15% return rate
                return_reasons = ['Defective', 'Not as described', 'Changed mind', 'Size issue', 'Damaged in shipping']
                
                return_item = {
                    'return_id': f'RET_{len(returns)+1:04d}',
                    'order_id': order['order_id'],
                    'product_id': order['product_id'],
                    'customer_id': order['customer_id'],
                    'reason': random.choice(return_reasons),
                    'return_date': order_date + timedelta(days=random.randint(1, 30)),
                    'days_since_purchase': random.randint(1, 30),
                    'refund_amount': order['total_amount'] * 0.9,  # 90% refund
                    'return_status': random.choice(['approved', 'pending', 'rejected'])
                }
                returns.append(return_item)
        
        db.orders.drop()
        db.orders.insert_many(orders)
        print(f"      ✅ Created {len(orders)} orders")
        
        if returns:
            db.returns.drop()
            db.returns.insert_many(returns)
            print(f"      ✅ Created {len(returns)} returns")
    
    def _generate_support_data(self, db):
        """Generate customer support data"""
        print("   🎧 Generating support tickets...")
        
        ticket_types = ['Product Issue', 'Shipping Problem', 'Billing Question', 'General Inquiry', 'Complaint']
        priorities = ['Low', 'Medium', 'High', 'Critical']
        
        tickets = []
        for i in range(80):
            created_date = datetime.now() - timedelta(days=random.randint(1, 180))
            resolution_time = random.randint(1, 10)  # days
            
            ticket = {
                'ticket_id': f'TKT_{i+1:04d}',
                'customer_id': f'CUST_{random.randint(1, 100):04d}',
                'ticket_type': random.choice(ticket_types),
                'priority': random.choice(priorities),
                'status': random.choice(['Open', 'In Progress', 'Resolved', 'Closed']),
                'created_date': created_date,
                'resolved_date': created_date + timedelta(days=resolution_time),
                'resolution_time_days': resolution_time,
                'resolution_rating': random.randint(1, 5),
                'agent_id': f'AGENT_{random.randint(1, 10):02d}',
                'satisfaction_rating': random.randint(1, 5),
                'description': f'Customer support ticket for {random.choice(ticket_types).lower()}'
            }
            tickets.append(ticket)
        
        db.support_tickets.drop()
        db.support_tickets.insert_many(tickets)
        print(f"      ✅ Created {len(tickets)} support tickets")
    
    def _generate_analytics_data(self, db):
        """Generate analytics and metrics data"""
        print("   📊 Generating analytics...")
        
        events = []
        metrics = []
        
        # User events
        event_types = ['page_view', 'product_view', 'add_to_cart', 'purchase', 'search', 'login']
        
        for i in range(1000):
            event = {
                'event_id': f'EVT_{i+1:04d}',
                'user_id': f'CUST_{random.randint(1, 100):04d}',
                'event_type': random.choice(event_types),
                'product_id': f'PROD_{random.randint(1, 200):04d}' if random.random() < 0.7 else None,
                'timestamp': datetime.now() - timedelta(days=random.randint(1, 90)),
                'session_id': f'SESS_{random.randint(1, 500):04d}',
                'page_url': f'/product/{random.randint(1, 200)}',
                'user_agent': 'Mozilla/5.0 (compatible browser)',
                'ip_address': f'192.168.1.{random.randint(1, 255)}'
            }
            events.append(event)
        
        # Daily metrics
        for i in range(90):  # 90 days of metrics
            date = datetime.now() - timedelta(days=i)
            
            metric = {
                'date': date,
                'daily_revenue': round(random.uniform(1000, 10000), 2),
                'daily_orders': random.randint(10, 100),
                'daily_visitors': random.randint(500, 2000),
                'conversion_rate': round(random.uniform(0.01, 0.05), 4),
                'average_order_value': round(random.uniform(50, 200), 2),
                'customer_acquisition_cost': round(random.uniform(10, 50), 2),
                'return_rate': round(random.uniform(0.05, 0.20), 4)
            }
            metrics.append(metric)
        
        db.user_events.drop()
        db.user_events.insert_many(events)
        print(f"      ✅ Created {len(events)} user events")
        
        db.daily_metrics.drop()
        db.daily_metrics.insert_many(metrics)
        print(f"      ✅ Created {len(metrics)} daily metrics")
    
    def print_data_summary(self):
        """Print summary of generated data"""
        if not self.client:
            print("❌ Not connected to MongoDB")
            return
        
        print(f"\n📊 GENERATED DATA SUMMARY")
        print(f"{'='*50}")
        
        databases = ['ecommerce_main', 'customer_management', 'order_processing', 'analytics_data']
        
        for db_name in databases:
            db = self.client[db_name]
            collections = db.list_collection_names()
            
            print(f"\n🗄️ Database: {db_name}")
            for collection_name in collections:
                count = db[collection_name].count_documents({})
                print(f"   📁 {collection_name}: {count:,} documents")


def main():
    """Main function to generate test data"""
    print("🧪 MongoDB Test Data Generator")
    print("="*40)
    
    connection_string = input("Enter MongoDB connection string (or press Enter for localhost): ").strip()
    if not connection_string:
        connection_string = "mongodb://localhost:27017/"
    
    generator = TestDataGenerator(connection_string)
    
    if not generator.connect():
        return
    
    print("\n⚠️  WARNING: This will drop existing test collections and create new data.")
    confirm = input("Continue? (y/N): ").strip().lower()
    
    if confirm != 'y':
        print("❌ Cancelled.")
        return
    
    generator.generate_ecommerce_data()
    generator.print_data_summary()
    
    print(f"\n✅ Test data generation completed!")
    print(f"🎯 You can now run the intelligent discovery POC with this data.")


if __name__ == "__main__":
    main()
