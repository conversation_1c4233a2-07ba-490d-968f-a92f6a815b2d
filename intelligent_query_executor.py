"""
Intelligent Query Executor - Actually executes queries and provides answers
"""

import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import json
from datetime import datetime
import traceback

from enhanced_cluster_manager import EnhancedMongoClusterManager, CollectionInfo
from collection_discovery_engine import IntelligentCollectionDiscovery, CollectionMatch
from business_glossary import ConceptExtractor

@dataclass
class QueryResult:
    """Result of executing a query"""
    success: bool
    data: List[Dict] = None
    count: int = 0
    error: str = None
    execution_time: float = 0.0
    query_used: str = None
    collection_used: str = None

@dataclass
class UserFriendlyResponse:
    """User-friendly response with natural language"""
    answer: str
    data_summary: str
    visualizations: List[Dict] = None
    follow_up_suggestions: List[str] = None
    raw_data: List[Dict] = None

class MongoQueryGenerator:
    """Generate MongoDB queries based on user intent and discovered collections"""
    
    def __init__(self):
        self.query_templates = self._initialize_query_templates()
    
    def _initialize_query_templates(self) -> Dict[str, Dict]:
        """Initialize query templates for different types of questions"""
        return {
            'find_product_by_name': {
                'pattern': r'(price|cost|info|details|about).*(oneplus|samsung|iphone|product)',
                'template': lambda name: [
                    {"$match": {"$or": [
                        {"name": {"$regex": name, "$options": "i"}},
                        {"product_name": {"$regex": name, "$options": "i"}},
                        {"title": {"$regex": name, "$options": "i"}}
                    ]}},
                    {"$limit": 10}
                ]
            },
            'find_most_expensive': {
                'pattern': r'(most|highest|costlier|expensive).*(price|cost|product)',
                'template': lambda: [
                    {"$match": {"$or": [
                        {"price": {"$exists": True, "$ne": None}},
                        {"cost": {"$exists": True, "$ne": None}},
                        {"amount": {"$exists": True, "$ne": None}}
                    ]}},
                    {"$sort": {"price": -1, "cost": -1, "amount": -1}},
                    {"$limit": 5}
                ]
            },
            'list_products': {
                'pattern': r'(show|list|display).*(products|items)',
                'template': lambda: [
                    {"$limit": 20},
                    {"$project": {"name": 1, "price": 1, "product_name": 1, "title": 1}}
                ]
            },
            'count_documents': {
                'pattern': r'(how many|count|total).*(products|items|movies|users)',
                'template': lambda: [
                    {"$count": "total"}
                ]
            },
            'find_by_rating': {
                'pattern': r'(rating|rated|score).*(high|low|best|worst)',
                'template': lambda: [
                    {"$match": {"$or": [
                        {"rating": {"$exists": True}},
                        {"imdb.rating": {"$exists": True}},
                        {"product_rating": {"$exists": True}}
                    ]}},
                    {"$sort": {"rating": -1, "imdb.rating": -1, "product_rating": -1}},
                    {"$limit": 10}
                ]
            },
            'recent_items': {
                'pattern': r'(recent|latest|new).*(products|movies|items)',
                'template': lambda: [
                    {"$sort": {"created_date": -1, "date": -1, "_id": -1}},
                    {"$limit": 10}
                ]
            },
            'collections_info': {
                'pattern': r'(collections|available|what).*(database|collections)',
                'template': lambda: []  # Special case - will be handled differently
            }
        }
    
    def generate_query(self, user_query: str, collection_info: CollectionInfo, concepts: Dict) -> Tuple[List[Dict], str]:
        """Generate appropriate MongoDB aggregation pipeline"""
        
        user_query_lower = user_query.lower()
        
        # Extract product name if mentioned
        product_name = self._extract_product_name(user_query)
        
        # Match query patterns
        for query_type, template_info in self.query_templates.items():
            if re.search(template_info['pattern'], user_query_lower):
                if query_type == 'find_product_by_name' and product_name:
                    return template_info['template'](product_name), f"Finding products matching '{product_name}'"
                elif query_type == 'collections_info':
                    return [], "Listing available collections"
                elif query_type == 'find_most_expensive':
                    return template_info['template'](), "Finding most expensive products"
                else:
                    return template_info['template'](), f"Executing {query_type} query"
        
        # Default query based on collection type
        return self._generate_default_query(collection_info, concepts)
    
    def _extract_product_name(self, query: str) -> Optional[str]:
        """Extract product name from query"""
        # Common product patterns
        product_patterns = [
            r'(oneplus\s+\d+\s+pro?)',
            r'(iphone\s+\d+\s+pro?)',
            r'(samsung\s+galaxy\s+\w+)',
            r'(pixel\s+\d+)',
            r'(macbook\s+\w+)',
        ]
        
        for pattern in product_patterns:
            match = re.search(pattern, query.lower())
            if match:
                return match.group(1)
        
        return None
    
    def _generate_default_query(self, collection_info: CollectionInfo, concepts: Dict) -> Tuple[List[Dict], str]:
        """Generate default query based on collection structure"""
        
        # Simple find with limit
        pipeline = [{"$limit": 10}]
        
        # Add projection for common fields
        common_fields = ["name", "title", "price", "rating", "created_date", "product_name"]
        available_fields = {field for field in collection_info.fields.keys() if not field.startswith('_')}
        
        projection = {}
        for field in common_fields:
            if field in available_fields:
                projection[field] = 1
        
        if projection:
            pipeline.append({"$project": projection})
        
        return pipeline, "Retrieving sample data"

class IntelligentQueryExecutor:
    """Main class that executes queries and provides intelligent responses"""
    
    def __init__(self, cluster_manager: EnhancedMongoClusterManager, discovery_engine: IntelligentCollectionDiscovery):
        self.cluster_manager = cluster_manager
        self.discovery_engine = discovery_engine
        self.query_generator = MongoQueryGenerator()
        self.concept_extractor = ConceptExtractor()
    
    def ask_database(self, user_query: str) -> UserFriendlyResponse:
        """Main method - ask a question and get a user-friendly answer"""
        
        try:
            print(f"\n🤖 Processing: '{user_query}'")
            
            # Step 1: Handle special queries
            if self._is_collections_query(user_query):
                return self._handle_collections_query(user_query)
            
            # Step 2: Discover relevant collections
            discovery_result = self.discovery_engine.find_relevant_collections(user_query)
            
            if not discovery_result.matches:
                return UserFriendlyResponse(
                    answer="I couldn't find any relevant collections for your query. Could you try rephrasing your question?",
                    data_summary="No relevant data found",
                    follow_up_suggestions=[
                        "Try asking about movies, users, products, or customers",
                        "Use more specific terms in your query",
                        "Ask 'what collections are available?' to see what data exists"
                    ]
                )
            
            # Step 3: Execute query on best matching collection
            best_match = discovery_result.matches[0]
            collection_info = self.cluster_manager.get_collection_info(best_match.database, best_match.collection)
            
            if not collection_info:
                return UserFriendlyResponse(
                    answer="Sorry, I couldn't access the collection data.",
                    data_summary="Collection access error"
                )
            
            # Step 4: Generate and execute query
            query_result = self._execute_query(user_query, collection_info, discovery_result.concepts)
            
            # Step 5: Generate user-friendly response
            return self._generate_user_friendly_response(user_query, query_result, best_match, discovery_result.concepts)
            
        except Exception as e:
            print(f"❌ Error in ask_database: {str(e)}")
            traceback.print_exc()
            return UserFriendlyResponse(
                answer=f"Sorry, I encountered an error while processing your query: {str(e)}",
                data_summary="Error occurred"
            )
    
    def _is_collections_query(self, query: str) -> bool:
        """Check if user is asking about available collections"""
        patterns = [
            r'what.*collections.*available',
            r'show.*collections',
            r'list.*collections',
            r'collections.*in.*database',
            r'what.*data.*available'
        ]
        
        query_lower = query.lower()
        return any(re.search(pattern, query_lower) for pattern in patterns)
    
    def _handle_collections_query(self, user_query: str) -> UserFriendlyResponse:
        """Handle queries about available collections"""
        
        # Check if asking about specific database
        database_match = re.search(r'(comics-generator|sample_mflix|database_assistant|chat_sessions|test)', user_query.lower())
        
        if database_match:
            db_name = database_match.group(1).replace('_', '-') if 'comics' in database_match.group(1) else database_match.group(1)
            # Map variations
            db_mapping = {
                'comics-generator': 'comics-generator',
                'sample_mflix': 'sample_mflix',
                'database_assistant': 'database_assistant',
                'chat_sessions': 'chat_sessions',
                'test': 'test'
            }
            
            actual_db_name = db_mapping.get(db_name, db_name)
            
            if actual_db_name in self.cluster_manager.cluster_info.databases:
                db_info = self.cluster_manager.cluster_info.databases[actual_db_name]
                
                collections_info = []
                for coll_name, coll_info in db_info.collections.items():
                    collections_info.append({
                        'name': coll_name,
                        'documents': coll_info.document_count,
                        'fields': len(coll_info.fields)
                    })
                
                answer = f"📊 **{actual_db_name}** database contains {len(collections_info)} collections:\n\n"
                for coll in sorted(collections_info, key=lambda x: x['documents'], reverse=True):
                    answer += f"• **{coll['name']}**: {coll['documents']:,} documents, {coll['fields']} fields\n"
                
                return UserFriendlyResponse(
                    answer=answer,
                    data_summary=f"Found {len(collections_info)} collections in {actual_db_name}",
                    raw_data=collections_info,
                    follow_up_suggestions=[
                        f"Ask about specific data: 'Show me products from {actual_db_name}'",
                        f"Get details: 'What's in the {collections_info[0]['name']} collection?'",
                        "Explore data: 'Show me some sample data'"
                    ]
                )
        
        # General collections overview
        total_collections = sum(len(db.collections) for db in self.cluster_manager.cluster_info.databases.values())
        
        answer = f"🗄️ **Your MongoDB cluster has {len(self.cluster_manager.cluster_info.databases)} databases with {total_collections} collections:**\n\n"
        
        for db_name, db_info in self.cluster_manager.cluster_info.databases.items():
            answer += f"**{db_name}** ({len(db_info.collections)} collections):\n"
            
            # Show top 3 collections by document count
            top_collections = sorted(
                [(name, info.document_count) for name, info in db_info.collections.items()],
                key=lambda x: x[1], reverse=True
            )[:3]
            
            for coll_name, doc_count in top_collections:
                answer += f"  • {coll_name}: {doc_count:,} documents\n"
            
            answer += "\n"
        
        return UserFriendlyResponse(
            answer=answer,
            data_summary=f"Cluster overview: {len(self.cluster_manager.cluster_info.databases)} databases, {total_collections} collections",
            follow_up_suggestions=[
                "Ask about specific data: 'Show me movies with high ratings'",
                "Explore products: 'What products are available?'",
                "Get user data: 'Show me user information'"
            ]
        )
    
    def _execute_query(self, user_query: str, collection_info: CollectionInfo, concepts: Dict) -> QueryResult:
        """Execute the actual MongoDB query"""
        
        start_time = datetime.now()
        
        try:
            # Generate query pipeline
            pipeline, description = self.query_generator.generate_query(user_query, collection_info, concepts)
            
            print(f"🔍 {description}")
            print(f"📋 Collection: {collection_info.database}.{collection_info.name}")
            
            # Execute query
            collection = collection_info.collection_object
            
            if pipeline:
                results = list(collection.aggregate(pipeline))
            else:
                # Simple find for basic queries
                results = list(collection.find().limit(10))
            
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            return QueryResult(
                success=True,
                data=results,
                count=len(results),
                execution_time=execution_time,
                query_used=str(pipeline),
                collection_used=f"{collection_info.database}.{collection_info.name}"
            )
            
        except Exception as e:
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            print(f"❌ Query execution error: {str(e)}")
            
            return QueryResult(
                success=False,
                error=str(e),
                execution_time=execution_time,
                collection_used=f"{collection_info.database}.{collection_info.name}"
            )
    
    def _generate_user_friendly_response(self, user_query: str, query_result: QueryResult, 
                                       best_match: CollectionMatch, concepts: Dict) -> UserFriendlyResponse:
        """Generate a user-friendly natural language response"""
        
        if not query_result.success:
            return UserFriendlyResponse(
                answer=f"I encountered an error while querying the {query_result.collection_used} collection: {query_result.error}",
                data_summary="Query execution failed"
            )
        
        if not query_result.data:
            return UserFriendlyResponse(
                answer=f"I searched the {query_result.collection_used} collection but didn't find any matching data for your query.",
                data_summary="No results found",
                follow_up_suggestions=[
                    "Try a different search term",
                    "Ask about what data is available in this collection",
                    "Try a broader query"
                ]
            )
        
        # Generate natural language response based on query type and results
        answer = self._create_natural_language_answer(user_query, query_result, concepts)
        data_summary = self._create_data_summary(query_result)
        suggestions = self._generate_follow_up_suggestions(user_query, query_result, concepts)
        
        return UserFriendlyResponse(
            answer=answer,
            data_summary=data_summary,
            raw_data=query_result.data[:5],  # Limit to first 5 results
            follow_up_suggestions=suggestions
        )
    
    def _create_natural_language_answer(self, user_query: str, query_result: QueryResult, concepts: Dict) -> str:
        """Create natural language answer based on the results"""
        
        data = query_result.data
        count = len(data)
        
        # Determine response type based on query
        if 'price' in user_query.lower() and any('oneplus' in user_query.lower() for _ in [1]):
            # Specific product price query
            if count > 0:
                # Look for price-related fields
                first_item = data[0]
                price_fields = ['price', 'cost', 'amount', 'value']
                price_info = None
                
                for field in price_fields:
                    if field in first_item:
                        price_info = first_item[field]
                        break
                
                product_name = first_item.get('name', first_item.get('product_name', first_item.get('title', 'the product')))
                
                if price_info:
                    return f"💰 I found pricing information! **{product_name}** costs **${price_info}**.\n\nThis data comes from the `{query_result.collection_used}` collection."
                else:
                    return f"📱 I found **{product_name}** in the database, but unfortunately the price information isn't available in this collection.\n\n**Available details:**\n" + \
                           "\n".join([f"• {k}: {v}" for k, v in first_item.items() if not k.startswith('_')][:5])
            else:
                return "❌ I couldn't find any OnePlus 10 Pro products in the database. The product might not be available or might be listed under a different name."
        
        elif 'collections' in user_query.lower() or 'available' in user_query.lower():
            # Collections query - already handled separately
            return f"📊 Found {count} items in the {query_result.collection_used} collection."
        
        elif count == 1:
            # Single result
            item = data[0]
            name = item.get('name', item.get('title', item.get('product_name', 'Item')))
            return f"🎯 I found **{name}**!\n\n**Details:**\n" + \
                   "\n".join([f"• **{k}**: {v}" for k, v in item.items() if not k.startswith('_') and v is not None][:8])
        
        elif count <= 5:
            # Few results - show all
            answer = f"📋 I found **{count} results** for your query:\n\n"
            for i, item in enumerate(data, 1):
                name = item.get('name', item.get('title', item.get('product_name', f'Item {i}')))
                answer += f"**{i}. {name}**\n"
                
                # Show key details
                key_fields = ['price', 'rating', 'category', 'type', 'description']
                details = []
                for field in key_fields:
                    if field in item and item[field] is not None:
                        details.append(f"{field}: {item[field]}")
                
                if details:
                    answer += "   " + " | ".join(details[:3]) + "\n"
                answer += "\n"
            
            return answer
        
        else:
            # Many results - show summary
            answer = f"📊 I found **{count} results** in the `{query_result.collection_used}` collection!\n\n"
            
            # Show first few items
            answer += "**Top results:**\n"
            for i, item in enumerate(data[:3], 1):
                name = item.get('name', item.get('title', item.get('product_name', f'Item {i}')))
                answer += f"{i}. {name}\n"
            
            if count > 3:
                answer += f"... and {count - 3} more items\n"
            
            return answer
    
    def _create_data_summary(self, query_result: QueryResult) -> str:
        """Create a brief data summary"""
        return f"Retrieved {query_result.count} records from {query_result.collection_used} in {query_result.execution_time:.2f}s"
    
    def _generate_follow_up_suggestions(self, user_query: str, query_result: QueryResult, concepts: Dict) -> List[str]:
        """Generate relevant follow-up suggestions"""
        
        suggestions = []
        
        if 'price' in user_query.lower():
            suggestions.extend([
                "Show me products with the highest ratings",
                "What other products are available?",
                "Compare prices across different products"
            ])
        elif 'movie' in user_query.lower():
            suggestions.extend([
                "Show me movies with the highest ratings",
                "Find recent movies",
                "Show me movie comments and reviews"
            ])
        elif 'product' in user_query.lower():
            suggestions.extend([
                "Show me product ratings",
                "Find products by category",
                "Show me recent products"
            ])
        else:
            suggestions.extend([
                "Show me more details about this data",
                "Find similar items",
                "Get statistics about this collection"
            ])
        
        return suggestions[:3]  # Limit to 3 suggestions
