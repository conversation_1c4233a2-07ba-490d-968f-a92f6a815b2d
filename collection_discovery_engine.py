"""
Intelligent Collection Discovery Engine
"""

from typing import List, Dict, Any, Optional, Tu<PERSON>
from dataclasses import dataclass, field
import math
import json
from datetime import datetime

from enhanced_cluster_manager import CollectionInfo, EnhancedMongoClusterManager
from business_glossary import ConceptExtractor

@dataclass
class CollectionMatch:
    """Represents a collection match with scoring details"""
    database: str
    collection: str
    schema_score: float
    content_score: float
    usage_score: float
    final_score: float
    confidence: str
    reasoning: List[str] = field(default_factory=list)
    field_matches: List[Dict] = field(default_factory=list)
    sample_data_relevance: float = 0.0

@dataclass
class DiscoveryResult:
    """Complete discovery result"""
    query: str
    concepts: Dict[str, Any]
    matches: List[CollectionMatch]
    total_collections_analyzed: int
    analysis_time: float
    recommendations: List[str] = field(default_factory=list)

class SchemaBasedDiscovery:
    """Analyze collection schemas to find relevant data"""
    
    def __init__(self, concept_extractor: ConceptExtractor):
        self.concept_extractor = concept_extractor
    
    def analyze_field_relevance(self, collection_info: CollectionInfo, concepts: Dict[str, Any]) -> <PERSON><PERSON>[float, List[Dict]]:
        """Score collection relevance based on field names and types"""
        
        if not collection_info.fields:
            return 0.0, []
        
        primary_concepts = concepts.get('primary_concepts', [])
        expanded_concepts = concepts.get('expanded_concepts', [])
        context_hints = concepts.get('context_hints', [])
        concept_weights = concepts.get('concept_weights', {})
        
        field_matches = []
        total_score = 0.0
        
        # Analyze each field in the collection
        for field_name, field_info in collection_info.fields.items():
            field_score = 0.0
            match_reasons = []
            
            # Check direct concept matches in field name
            for concept in primary_concepts:
                similarity = self._calculate_field_similarity(field_name, concept)
                if similarity > 0.5:
                    weight = concept_weights.get(concept, 1.0)
                    field_score += similarity * weight
                    match_reasons.append(f"Direct match with '{concept}' (similarity: {similarity:.2f})")
            
            # Check expanded concept matches
            for concept in expanded_concepts:
                if concept not in primary_concepts:  # Avoid double counting
                    similarity = self._calculate_field_similarity(field_name, concept)
                    if similarity > 0.6:  # Higher threshold for expanded concepts
                        field_score += similarity * 0.7  # Lower weight for synonyms
                        match_reasons.append(f"Synonym match with '{concept}' (similarity: {similarity:.2f})")
            
            # Check context hints
            for hint in context_hints:
                if hint.lower() in field_name.lower():
                    field_score += 0.5
                    match_reasons.append(f"Context hint match: '{hint}'")
            
            # Boost score for certain field types
            type_boost = self._get_type_relevance_boost(field_info.type, concepts)
            field_score += type_boost
            
            if field_score > 0.3:  # Only include significant matches
                field_matches.append({
                    'field_name': field_name,
                    'field_type': field_info.type,
                    'score': field_score,
                    'frequency': field_info.frequency,
                    'sample_values': field_info.sample_values[:3],  # First 3 sample values
                    'reasons': match_reasons
                })
                
                total_score += field_score
        
        # Normalize score based on number of fields (avoid bias toward collections with many fields)
        if len(collection_info.fields) > 0:
            normalized_score = total_score / math.sqrt(len(collection_info.fields))
        else:
            normalized_score = 0.0
        
        # Boost collections with multiple relevant fields
        if len(field_matches) > 1:
            normalized_score *= 1.2
        
        # Cap the score at 1.0
        final_score = min(normalized_score, 1.0)
        
        return final_score, field_matches
    
    def _calculate_field_similarity(self, field_name: str, concept: str) -> float:
        """Calculate semantic similarity between field name and concept"""
        field_lower = field_name.lower()
        concept_lower = concept.lower()
        
        # Exact match
        if concept_lower == field_lower:
            return 1.0
        
        # Substring match
        if concept_lower in field_lower:
            return 0.9
        
        if field_lower in concept_lower:
            return 0.8
        
        # Partial matches with common patterns
        # Handle snake_case and camelCase
        field_parts = field_lower.replace('_', ' ').replace('-', ' ').split()
        concept_parts = concept_lower.replace('_', ' ').replace('-', ' ').split()
        
        # Check if any part matches
        for field_part in field_parts:
            for concept_part in concept_parts:
                if field_part == concept_part:
                    return 0.7
                if concept_part in field_part or field_part in concept_part:
                    return 0.6
        
        # Fuzzy matching for common variations
        similarity_patterns = [
            ('customer', 'user'), ('customer', 'client'), ('user', 'client'),
            ('product', 'item'), ('order', 'purchase'), ('rating', 'score'),
            ('date', 'time'), ('created', 'date'), ('updated', 'modified')
        ]
        
        for pattern1, pattern2 in similarity_patterns:
            if (pattern1 in field_lower and pattern2 in concept_lower) or \
               (pattern2 in field_lower and pattern1 in concept_lower):
                return 0.5
        
        return 0.0
    
    def _get_type_relevance_boost(self, field_type: str, concepts: Dict[str, Any]) -> float:
        """Get relevance boost based on field type and query intent"""
        intent = concepts.get('intent', {})
        data_requirements = intent.get('data_requirements', [])
        
        boost = 0.0
        
        # Numeric fields for analytics queries
        if field_type in ['int', 'float', 'Decimal128'] and 'numeric_data' in data_requirements:
            boost += 0.2
        
        # Date fields for temporal queries
        if field_type in ['datetime', 'date'] and 'temporal_data' in data_requirements:
            boost += 0.3
        
        # String fields for text analysis
        if field_type == 'str' and intent.get('type') == 'satisfaction_analysis':
            boost += 0.1
        
        return boost

class ContentBasedDiscovery:
    """Analyze actual data content to determine relevance"""
    
    def analyze_data_content(self, collection_info: CollectionInfo, concepts: Dict[str, Any]) -> float:
        """Analyze sample documents to understand data content"""
        
        if not collection_info.sample_documents:
            return 0.0
        
        primary_concepts = concepts.get('primary_concepts', [])
        expanded_concepts = concepts.get('expanded_concepts', [])
        
        total_relevance = 0.0
        analyzed_docs = 0
        
        for doc in collection_info.sample_documents[:20]:  # Analyze up to 20 sample docs
            doc_relevance = self._analyze_document_content(doc, primary_concepts, expanded_concepts)
            total_relevance += doc_relevance
            analyzed_docs += 1
        
        if analyzed_docs == 0:
            return 0.0
        
        average_relevance = total_relevance / analyzed_docs
        return min(average_relevance, 1.0)
    
    def _analyze_document_content(self, document: Dict, primary_concepts: List[str], expanded_concepts: List[str]) -> float:
        """Analyze a single document for concept relevance"""
        relevance_score = 0.0
        
        # Convert document to searchable text
        doc_text = self._extract_text_from_document(document)
        doc_text_lower = doc_text.lower()
        
        # Check for primary concept mentions
        for concept in primary_concepts:
            if concept.lower() in doc_text_lower:
                relevance_score += 0.3
        
        # Check for expanded concept mentions (lower weight)
        for concept in expanded_concepts:
            if concept not in primary_concepts and concept.lower() in doc_text_lower:
                relevance_score += 0.1
        
        # Check for numeric patterns that might indicate ratings/scores
        if any(concept in ['satisfaction', 'rating', 'score'] for concept in primary_concepts):
            numeric_fields = self._extract_numeric_fields(document)
            for field_name, value in numeric_fields.items():
                if isinstance(value, (int, float)) and 0 <= value <= 10:  # Typical rating range
                    relevance_score += 0.2
        
        return min(relevance_score, 1.0)
    
    def _extract_text_from_document(self, document: Dict, prefix: str = "") -> str:
        """Extract all text content from a document"""
        text_parts = []
        
        for key, value in document.items():
            if isinstance(value, str):
                text_parts.append(f"{key}: {value}")
            elif isinstance(value, dict) and prefix.count('.') < 2:  # Limit recursion depth
                nested_text = self._extract_text_from_document(value, f"{prefix}.{key}")
                text_parts.append(nested_text)
            elif isinstance(value, list) and len(value) > 0 and isinstance(value[0], str):
                text_parts.append(f"{key}: {' '.join(value[:5])}")  # First 5 items
        
        return " ".join(text_parts)
    
    def _extract_numeric_fields(self, document: Dict) -> Dict[str, Any]:
        """Extract numeric fields from a document"""
        numeric_fields = {}
        
        for key, value in document.items():
            if isinstance(value, (int, float)):
                numeric_fields[key] = value
            elif isinstance(value, dict):
                nested_numeric = self._extract_numeric_fields(value)
                for nested_key, nested_value in nested_numeric.items():
                    numeric_fields[f"{key}.{nested_key}"] = nested_value
        
        return numeric_fields

class UsagePatternDiscovery:
    """Learn from historical query patterns (simplified for POC)"""
    
    def __init__(self):
        # In a real implementation, this would connect to a usage database
        # For POC, we'll use some mock historical data
        self.mock_usage_data = self._initialize_mock_usage_data()
    
    def _initialize_mock_usage_data(self) -> Dict[str, Dict]:
        """Initialize mock usage data for testing"""
        return {
            'satisfaction_queries': {
                'collections_used': ['reviews.ratings', 'feedback.surveys', 'support.tickets'],
                'success_rates': [0.92, 0.85, 0.71],
                'user_satisfaction': [4.2, 4.0, 3.5],
                'query_count': 15
            },
            'product_queries': {
                'collections_used': ['products.catalog', 'orders.items', 'inventory.stock'],
                'success_rates': [0.95, 0.88, 0.76],
                'user_satisfaction': [4.5, 4.1, 3.8],
                'query_count': 23
            },
            'sales_queries': {
                'collections_used': ['orders.transactions', 'payments.records', 'analytics.revenue'],
                'success_rates': [0.91, 0.87, 0.82],
                'user_satisfaction': [4.3, 4.0, 3.9],
                'query_count': 18
            }
        }
    
    def analyze_usage_patterns(self, concepts: Dict[str, Any]) -> Dict[str, float]:
        """Analyze historical usage to predict relevant collections"""
        primary_concepts = concepts.get('primary_concepts', [])
        collection_scores = {}
        
        # Simple pattern matching based on concepts
        for concept in primary_concepts:
            if concept in ['satisfaction', 'rating', 'feedback']:
                pattern_data = self.mock_usage_data.get('satisfaction_queries', {})
            elif concept in ['product', 'item', 'catalog']:
                pattern_data = self.mock_usage_data.get('product_queries', {})
            elif concept in ['sales', 'revenue', 'order']:
                pattern_data = self.mock_usage_data.get('sales_queries', {})
            else:
                continue
            
            collections = pattern_data.get('collections_used', [])
            success_rates = pattern_data.get('success_rates', [])
            satisfaction_scores = pattern_data.get('user_satisfaction', [])
            
            for i, collection in enumerate(collections):
                if i < len(success_rates) and i < len(satisfaction_scores):
                    # Calculate usage score based on success rate and user satisfaction
                    usage_score = (success_rates[i] * satisfaction_scores[i]) / 5.0  # Normalize to 0-1
                    
                    if collection in collection_scores:
                        collection_scores[collection] = max(collection_scores[collection], usage_score)
                    else:
                        collection_scores[collection] = usage_score
        
        return collection_scores

class IntelligentCollectionDiscovery:
    """Main class that orchestrates collection discovery"""
    
    def __init__(self, cluster_manager: EnhancedMongoClusterManager):
        self.cluster_manager = cluster_manager
        self.concept_extractor = ConceptExtractor()
        self.schema_analyzer = SchemaBasedDiscovery(self.concept_extractor)
        self.content_analyzer = ContentBasedDiscovery()
        self.usage_analyzer = UsagePatternDiscovery()
    
    def find_relevant_collections(self, user_query: str) -> DiscoveryResult:
        """Main method to find relevant collections for a user query"""
        
        start_time = datetime.now()
        
        print(f"🔍 Analyzing query: '{user_query}'")
        
        # Step 1: Extract concepts from user query
        concepts = self.concept_extractor.extract_query_concepts(user_query)
        print(f"📝 Extracted concepts: {concepts['primary_concepts']}")
        print(f"🎯 Query intent: {concepts['intent']['type']} (confidence: {concepts['intent']['confidence']})")
        
        # Step 2: Get all available collections
        all_collections = self.cluster_manager.get_all_collections()
        print(f"🗄️ Analyzing {len(all_collections)} collections across {len(self.cluster_manager.cluster_info.databases)} databases")
        
        # Step 3: Score each collection
        collection_matches = []
        
        for collection_info in all_collections:
            print(f"   Analyzing {collection_info.database}.{collection_info.name}...")
            
            # Schema-based scoring
            schema_score, field_matches = self.schema_analyzer.analyze_field_relevance(collection_info, concepts)
            
            # Content-based scoring
            content_score = self.content_analyzer.analyze_data_content(collection_info, concepts)
            
            # Usage pattern scoring
            usage_patterns = self.usage_analyzer.analyze_usage_patterns(concepts)
            collection_key = f"{collection_info.database}.{collection_info.name}"
            usage_score = usage_patterns.get(collection_key, 0.0)
            
            # Combined scoring with weights
            final_score = (
                schema_score * 0.5 +      # 50% weight on schema
                content_score * 0.3 +     # 30% weight on content  
                usage_score * 0.2         # 20% weight on usage
            )
            
            # Generate reasoning
            reasoning = self._generate_reasoning(collection_info, concepts, schema_score, content_score, usage_score, field_matches)
            
            # Determine confidence level
            confidence = self._calculate_confidence(schema_score, content_score, usage_score)
            
            if final_score > 0.1:  # Only include collections with some relevance
                collection_matches.append(CollectionMatch(
                    database=collection_info.database,
                    collection=collection_info.name,
                    schema_score=schema_score,
                    content_score=content_score,
                    usage_score=usage_score,
                    final_score=final_score,
                    confidence=confidence,
                    reasoning=reasoning,
                    field_matches=field_matches,
                    sample_data_relevance=content_score
                ))
        
        # Step 4: Sort by final score and filter
        collection_matches.sort(key=lambda x: x.final_score, reverse=True)
        
        # Filter out very low-confidence matches
        relevant_matches = [
            match for match in collection_matches 
            if match.final_score > 0.2  # Minimum relevance threshold
        ]
        
        end_time = datetime.now()
        analysis_time = (end_time - start_time).total_seconds()
        
        print(f"✅ Found {len(relevant_matches)} relevant collections in {analysis_time:.2f} seconds")
        
        # Generate recommendations
        recommendations = self._generate_recommendations(relevant_matches, concepts)
        
        return DiscoveryResult(
            query=user_query,
            concepts=concepts,
            matches=relevant_matches,
            total_collections_analyzed=len(all_collections),
            analysis_time=analysis_time,
            recommendations=recommendations
        )
    
    def _generate_reasoning(self, collection_info: CollectionInfo, concepts: Dict, schema_score: float, 
                          content_score: float, usage_score: float, field_matches: List[Dict]) -> List[str]:
        """Generate human-readable reasoning for the collection match"""
        reasoning = []
        
        if schema_score > 0.7:
            reasoning.append(f"Strong schema match (score: {schema_score:.2f}) - relevant field names found")
        elif schema_score > 0.4:
            reasoning.append(f"Moderate schema match (score: {schema_score:.2f}) - some relevant fields")
        
        if content_score > 0.5:
            reasoning.append(f"Good content relevance (score: {content_score:.2f}) - sample data contains relevant information")
        
        if usage_score > 0.6:
            reasoning.append(f"High usage score (score: {usage_score:.2f}) - frequently used for similar queries")
        
        if field_matches:
            top_matches = sorted(field_matches, key=lambda x: x['score'], reverse=True)[:3]
            field_names = [match['field_name'] for match in top_matches]
            reasoning.append(f"Key field matches: {', '.join(field_names)}")
        
        if collection_info.document_count > 1000:
            reasoning.append(f"Large dataset ({collection_info.document_count:,} documents) - substantial data available")
        
        return reasoning
    
    def _calculate_confidence(self, schema_score: float, content_score: float, usage_score: float) -> str:
        """Calculate confidence level based on scores"""
        avg_score = (schema_score + content_score + usage_score) / 3
        
        if avg_score > 0.7:
            return "HIGH"
        elif avg_score > 0.4:
            return "MEDIUM"
        else:
            return "LOW"
    
    def _generate_recommendations(self, matches: List[CollectionMatch], concepts: Dict) -> List[str]:
        """Generate actionable recommendations based on discovery results"""
        recommendations = []
        
        if not matches:
            recommendations.append("No highly relevant collections found. Consider refining your query or checking if the data exists.")
            return recommendations
        
        top_match = matches[0]
        if top_match.confidence == "HIGH":
            recommendations.append(f"Primary recommendation: Use {top_match.database}.{top_match.collection} (confidence: {top_match.confidence})")
        
        if len(matches) > 1 and matches[1].final_score > 0.5:
            recommendations.append(f"Secondary option: {matches[1].database}.{matches[1].collection} for additional context")
        
        # Intent-specific recommendations
        intent_type = concepts.get('intent', {}).get('type', 'unknown')
        if intent_type == 'temporal_analysis':
            recommendations.append("For trend analysis, ensure the selected collections have date/time fields")
        elif intent_type == 'satisfaction_analysis':
            recommendations.append("Look for rating, score, or feedback fields in the selected collections")
        
        return recommendations
