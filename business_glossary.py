"""
Business Glossary and Concept Extraction for MongoDB Collection Discovery
"""

import re
from typing import List, Dict, Set, Tuple, Any
from dataclasses import dataclass
import json

@dataclass
class ConceptInfo:
    """Information about a business concept"""
    primary_term: str
    synonyms: List[str]
    category: str
    weight: float = 1.0
    context_hints: List[str] = None

class BusinessGlossary:
    """Business terminology and concept mapping"""
    
    def __init__(self):
        self.concept_map = self._initialize_business_concepts()
        self.stop_words = self._get_stop_words()
        
    def _initialize_business_concepts(self) -> Dict[str, ConceptInfo]:
        """Initialize business concept mappings"""
        concepts = {
            # Customer-related concepts
            'customer': ConceptInfo(
                primary_term='customer',
                synonyms=['user', 'client', 'buyer', 'purchaser', 'consumer', 'account', 'member'],
                category='customer',
                weight=1.0,
                context_hints=['id', 'name', 'email', 'profile']
            ),
            
            # Satisfaction and feedback concepts
            'satisfaction': ConceptInfo(
                primary_term='satisfaction',
                synonyms=['rating', 'score', 'feedback', 'review', 'nps', 'csat', 'sentiment', 'opinion'],
                category='feedback',
                weight=1.2,  # Higher weight for satisfaction queries
                context_hints=['rating', 'score', 'stars', 'thumbs', 'like', 'dislike']
            ),
            
            # Product-related concepts
            'product': ConceptInfo(
                primary_term='product',
                synonyms=['item', 'goods', 'merchandise', 'catalog', 'inventory', 'sku', 'article'],
                category='product',
                weight=1.0,
                context_hints=['name', 'title', 'description', 'category', 'brand']
            ),
            
            # Sales and revenue concepts
            'sales': ConceptInfo(
                primary_term='sales',
                synonyms=['revenue', 'income', 'earnings', 'profit', 'amount', 'price', 'cost', 'value'],
                category='financial',
                weight=1.0,
                context_hints=['amount', 'total', 'sum', 'price', 'cost']
            ),
            
            # Order and transaction concepts
            'order': ConceptInfo(
                primary_term='order',
                synonyms=['transaction', 'purchase', 'sale', 'checkout', 'payment', 'invoice'],
                category='transaction',
                weight=1.0,
                context_hints=['id', 'number', 'date', 'status', 'total']
            ),
            
            # Temporal concepts
            'trend': ConceptInfo(
                primary_term='trend',
                synonyms=['time', 'date', 'period', 'history', 'timeline', 'over time', 'temporal', 'series'],
                category='temporal',
                weight=1.1,
                context_hints=['date', 'time', 'created', 'updated', 'timestamp']
            ),
            
            # Analytics concepts
            'analytics': ConceptInfo(
                primary_term='analytics',
                synonyms=['metrics', 'stats', 'statistics', 'data', 'insights', 'analysis', 'report'],
                category='analytics',
                weight=1.0,
                context_hints=['count', 'sum', 'avg', 'max', 'min', 'total']
            ),
            
            # User behavior concepts
            'behavior': ConceptInfo(
                primary_term='behavior',
                synonyms=['activity', 'action', 'event', 'interaction', 'engagement', 'usage'],
                category='behavior',
                weight=1.0,
                context_hints=['event', 'action', 'click', 'view', 'session']
            ),
            
            # Support and service concepts
            'support': ConceptInfo(
                primary_term='support',
                synonyms=['service', 'help', 'ticket', 'issue', 'problem', 'complaint', 'inquiry'],
                category='support',
                weight=1.0,
                context_hints=['ticket', 'status', 'priority', 'resolution', 'agent']
            ),
            
            # Inventory concepts
            'inventory': ConceptInfo(
                primary_term='inventory',
                synonyms=['stock', 'warehouse', 'supply', 'availability', 'quantity', 'storage'],
                category='inventory',
                weight=1.0,
                context_hints=['quantity', 'stock', 'available', 'warehouse', 'location']
            ),

            # Communication concepts
            'chat': ConceptInfo(
                primary_term='chat',
                synonyms=['message', 'conversation', 'communication', 'dialogue', 'talk', 'discussion'],
                category='communication',
                weight=1.0,
                context_hints=['message', 'text', 'content', 'sender', 'timestamp']
            ),

            # Movie/Entertainment concepts
            'movie': ConceptInfo(
                primary_term='movie',
                synonyms=['film', 'cinema', 'video', 'entertainment', 'show', 'title'],
                category='entertainment',
                weight=1.0,
                context_hints=['title', 'genre', 'director', 'cast', 'year']
            ),

            # Comics/Character concepts
            'character': ConceptInfo(
                primary_term='character',
                synonyms=['hero', 'protagonist', 'figure', 'persona', 'role'],
                category='entertainment',
                weight=1.0,
                context_hints=['name', 'description', 'powers', 'story']
            )
        }
        
        return concepts
    
    def _get_stop_words(self) -> Set[str]:
        """Get common stop words to filter out"""
        return {
            'show', 'me', 'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 
            'of', 'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 
            'after', 'above', 'below', 'between', 'among', 'through', 'during', 'before', 
            'after', 'above', 'below', 'up', 'down', 'out', 'off', 'over', 'under', 'again', 
            'further', 'then', 'once', 'here', 'there', 'when', 'where', 'why', 'how', 'all', 
            'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such', 'no', 'nor', 
            'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very', 'can', 'will', 'just', 
            'should', 'now', 'get', 'give', 'create', 'make', 'display', 'find', 'list'
        }
    
    def extract_concepts(self, user_query: str) -> List[str]:
        """Extract business concepts from user query"""
        # Clean and tokenize the query
        cleaned_query = self._clean_query(user_query)
        tokens = self._tokenize(cleaned_query)
        
        # Remove stop words
        meaningful_tokens = [token for token in tokens if token not in self.stop_words]
        
        # Extract concepts
        extracted_concepts = []
        
        # Direct concept matching
        for token in meaningful_tokens:
            concepts = self._find_matching_concepts(token)
            extracted_concepts.extend(concepts)
        
        # Phrase matching (for multi-word concepts)
        phrases = self._extract_phrases(cleaned_query)
        for phrase in phrases:
            concepts = self._find_matching_concepts(phrase)
            extracted_concepts.extend(concepts)
        
        # Remove duplicates while preserving order
        unique_concepts = []
        seen = set()
        for concept in extracted_concepts:
            if concept not in seen:
                unique_concepts.append(concept)
                seen.add(concept)
        
        return unique_concepts
    
    def _clean_query(self, query: str) -> str:
        """Clean and normalize the query"""
        # Convert to lowercase
        query = query.lower()
        
        # Remove special characters but keep spaces and hyphens
        query = re.sub(r'[^\w\s\-]', ' ', query)
        
        # Normalize whitespace
        query = re.sub(r'\s+', ' ', query).strip()
        
        return query
    
    def _tokenize(self, query: str) -> List[str]:
        """Tokenize the query into words"""
        return [token.strip() for token in query.split() if token.strip()]
    
    def _extract_phrases(self, query: str) -> List[str]:
        """Extract meaningful phrases from the query"""
        phrases = []
        
        # Common business phrases
        business_phrases = [
            'customer satisfaction', 'user experience', 'sales performance', 
            'order fulfillment', 'product performance', 'return rate',
            'conversion rate', 'customer retention', 'market share',
            'revenue growth', 'profit margin', 'customer lifetime value'
        ]
        
        for phrase in business_phrases:
            if phrase in query:
                phrases.append(phrase)
        
        # Extract bigrams and trigrams
        tokens = self._tokenize(query)
        
        # Bigrams
        for i in range(len(tokens) - 1):
            bigram = f"{tokens[i]} {tokens[i+1]}"
            if self._is_meaningful_phrase(bigram):
                phrases.append(bigram)
        
        # Trigrams
        for i in range(len(tokens) - 2):
            trigram = f"{tokens[i]} {tokens[i+1]} {tokens[i+2]}"
            if self._is_meaningful_phrase(trigram):
                phrases.append(trigram)
        
        return phrases
    
    def _is_meaningful_phrase(self, phrase: str) -> bool:
        """Check if a phrase is meaningful for business context"""
        # Skip phrases that are all stop words
        tokens = phrase.split()
        if all(token in self.stop_words for token in tokens):
            return False
        
        # Skip very short phrases
        if len(phrase) < 4:
            return False
        
        return True
    
    def _find_matching_concepts(self, term: str) -> List[str]:
        """Find business concepts that match a term"""
        matching_concepts = []
        
        for concept_key, concept_info in self.concept_map.items():
            # Check primary term
            if term == concept_info.primary_term:
                matching_concepts.append(concept_key)
                continue
            
            # Check synonyms
            for synonym in concept_info.synonyms:
                if term == synonym or term in synonym or synonym in term:
                    matching_concepts.append(concept_key)
                    break
        
        return matching_concepts
    
    def get_synonyms(self, concept: str) -> List[str]:
        """Get synonyms for a concept"""
        if concept in self.concept_map:
            return self.concept_map[concept].synonyms
        return []
    
    def get_concept_weight(self, concept: str) -> float:
        """Get the weight/importance of a concept"""
        if concept in self.concept_map:
            return self.concept_map[concept].weight
        return 1.0
    
    def get_context_hints(self, concept: str) -> List[str]:
        """Get context hints for a concept (field names that might be relevant)"""
        if concept in self.concept_map:
            return self.concept_map[concept].context_hints or []
        return []
    
    def expand_concepts(self, concepts: List[str]) -> List[str]:
        """Expand concepts with their synonyms"""
        expanded = []
        
        for concept in concepts:
            expanded.append(concept)
            synonyms = self.get_synonyms(concept)
            expanded.extend(synonyms)
        
        # Remove duplicates
        return list(set(expanded))
    
    def categorize_query(self, concepts: List[str]) -> Dict[str, List[str]]:
        """Categorize extracted concepts by business domain"""
        categories = {}
        
        for concept in concepts:
            if concept in self.concept_map:
                category = self.concept_map[concept].category
                if category not in categories:
                    categories[category] = []
                categories[category].append(concept)
        
        return categories


class ConceptExtractor:
    """Main class for extracting concepts from user queries"""
    
    def __init__(self):
        self.business_glossary = BusinessGlossary()
    
    def extract_query_concepts(self, user_query: str) -> Dict[str, Any]:
        """Extract comprehensive concept information from user query"""
        
        # Extract basic concepts
        concepts = self.business_glossary.extract_concepts(user_query)
        
        # Expand with synonyms
        expanded_concepts = self.business_glossary.expand_concepts(concepts)
        
        # Categorize concepts
        categories = self.business_glossary.categorize_query(concepts)
        
        # Determine query intent
        intent = self._determine_query_intent(concepts, categories)
        
        # Get context hints
        context_hints = []
        for concept in concepts:
            hints = self.business_glossary.get_context_hints(concept)
            context_hints.extend(hints)
        
        return {
            'original_query': user_query,
            'primary_concepts': concepts,
            'expanded_concepts': list(set(expanded_concepts)),
            'categories': categories,
            'intent': intent,
            'context_hints': list(set(context_hints)),
            'concept_weights': {
                concept: self.business_glossary.get_concept_weight(concept) 
                for concept in concepts
            }
        }
    
    def _determine_query_intent(self, concepts: List[str], categories: Dict[str, List[str]]) -> Dict[str, Any]:
        """Determine the intent of the user query"""
        intent = {
            'type': 'unknown',
            'confidence': 0.0,
            'data_requirements': [],
            'visualization_hint': None
        }
        
        # Check for temporal analysis
        if 'trend' in concepts or 'temporal' in categories:
            intent['type'] = 'temporal_analysis'
            intent['confidence'] = 0.8
            intent['data_requirements'] = ['temporal_data', 'numeric_data']
            intent['visualization_hint'] = 'line_chart'
        
        # Check for satisfaction analysis
        elif 'satisfaction' in concepts or 'feedback' in categories:
            intent['type'] = 'satisfaction_analysis'
            intent['confidence'] = 0.9
            intent['data_requirements'] = ['rating_data', 'customer_data']
            intent['visualization_hint'] = 'bar_chart'
        
        # Check for product analysis
        elif 'product' in concepts:
            intent['type'] = 'product_analysis'
            intent['confidence'] = 0.7
            intent['data_requirements'] = ['product_data', 'sales_data']
            intent['visualization_hint'] = 'pie_chart'
        
        # Check for financial analysis
        elif 'financial' in categories:
            intent['type'] = 'financial_analysis'
            intent['confidence'] = 0.8
            intent['data_requirements'] = ['financial_data', 'temporal_data']
            intent['visualization_hint'] = 'line_chart'
        
        return intent


if __name__ == "__main__":
    # Test the concept extraction
    extractor = ConceptExtractor()
    
    test_queries = [
        "Show me customer satisfaction trends over the last 6 months",
        "What are our top selling products by revenue?",
        "Display user registration patterns this year",
        "Analyze order fulfillment performance",
        "Create a chart of support ticket resolution times",
        "Show product return rates by category"
    ]
    
    print("🧠 CONCEPT EXTRACTION TESTING")
    print("="*60)
    
    for query in test_queries:
        print(f"\n📝 Query: '{query}'")
        result = extractor.extract_query_concepts(query)
        
        print(f"🎯 Primary concepts: {result['primary_concepts']}")
        print(f"📊 Categories: {result['categories']}")
        print(f"🔍 Intent: {result['intent']['type']} (confidence: {result['intent']['confidence']})")
        print(f"💡 Context hints: {result['context_hints'][:5]}")  # Show first 5 hints
