"""
Ask Your Database - Complete RAG-like system for MongoDB
"""

import sys
import traceback
from typing import Dict, Any
import json
from datetime import datetime

from enhanced_cluster_manager import EnhancedMongoClusterManager
from collection_discovery_engine import IntelligentCollectionDiscovery
from intelligent_query_executor import IntelligentQueryExecutor
from business_glossary import ConceptExtractor

class AskYourDatabase:
    """Complete 'Ask Your Database' system with RAG-like capabilities"""
    
    def __init__(self):
        self.cluster_manager = None
        self.discovery_engine = None
        self.query_executor = None
        self.is_initialized = False
    
    def initialize(self, connection_string: str) -> bool:
        """Initialize the complete system"""
        try:
            print("🚀 ASK YOUR DATABASE - INTELLIGENT MONGODB ASSISTANT")
            print("="*70)
            
            # Initialize cluster manager
            print("📡 Connecting to MongoDB cluster...")
            self.cluster_manager = EnhancedMongoClusterManager(connection_string)
            
            # Connect and analyze cluster
            summary = self.cluster_manager.connect_and_analyze_cluster()
            
            if "error" in summary:
                print(f"❌ Failed to connect: {summary['error']}")
                return False
            
            # Initialize discovery engine
            print("🧠 Initializing intelligent discovery...")
            self.discovery_engine = IntelligentCollectionDiscovery(self.cluster_manager)
            
            # Initialize query executor
            print("⚡ Initializing query executor...")
            self.query_executor = IntelligentQueryExecutor(self.cluster_manager, self.discovery_engine)
            
            self.is_initialized = True
            
            # Print brief summary
            total_collections = sum(len(db.collections) for db in self.cluster_manager.cluster_info.databases.values())
            total_docs = sum(db.total_documents for db in self.cluster_manager.cluster_info.databases.values())
            
            print(f"\n✅ System ready!")
            print(f"📊 Connected to {len(self.cluster_manager.cluster_info.databases)} databases")
            print(f"📁 {total_collections} collections available")
            print(f"📄 {total_docs:,} total documents")
            print(f"🎯 Ready to answer your questions!")
            
            return True
            
        except Exception as e:
            print(f"❌ Initialization failed: {str(e)}")
            traceback.print_exc()
            return False
    
    def ask(self, question: str) -> Dict[str, Any]:
        """Ask a question and get an intelligent answer"""
        if not self.is_initialized:
            return {"error": "System not initialized. Please run initialize() first."}
        
        try:
            # Get intelligent response
            response = self.query_executor.ask_database(question)
            
            # Print user-friendly response
            self._print_response(response)
            
            # Return structured response
            return {
                "question": question,
                "answer": response.answer,
                "data_summary": response.data_summary,
                "raw_data": response.raw_data,
                "follow_up_suggestions": response.follow_up_suggestions,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            error_msg = f"Failed to process question: {str(e)}"
            print(f"❌ {error_msg}")
            traceback.print_exc()
            return {"error": error_msg}
    
    def _print_response(self, response):
        """Print user-friendly response"""
        print(f"\n{'='*70}")
        print(f"🤖 ANSWER")
        print(f"{'='*70}")
        print(response.answer)
        
        if response.raw_data:
            print(f"\n📊 DATA PREVIEW:")
            for i, item in enumerate(response.raw_data[:3], 1):
                print(f"   {i}. {json.dumps(item, indent=6, default=str)[:200]}...")
        
        if response.follow_up_suggestions:
            print(f"\n💡 FOLLOW-UP SUGGESTIONS:")
            for suggestion in response.follow_up_suggestions:
                print(f"   • {suggestion}")
        
        print(f"\n📈 {response.data_summary}")
    
    def run_interactive_mode(self):
        """Run interactive question-answering mode"""
        if not self.is_initialized:
            print("❌ System not initialized. Please run initialize() first.")
            return
        
        print(f"\n🎮 INTERACTIVE MODE - ASK YOUR DATABASE")
        print(f"{'='*70}")
        print("Ask natural language questions about your MongoDB data!")
        print("Type 'quit' to exit, 'help' for examples, or 'stats' for cluster info.")
        
        while True:
            try:
                question = input("\n❓ Your question: ").strip()
                
                if question.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                
                elif question.lower() == 'help':
                    self._show_example_questions()
                    continue
                
                elif question.lower() == 'stats':
                    self.cluster_manager.print_cluster_summary()
                    continue
                
                elif not question:
                    print("Please ask a question or type 'help' for examples.")
                    continue
                
                # Process the question
                self.ask(question)
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {str(e)}")
    
    def _show_example_questions(self):
        """Show example questions users can ask"""
        examples = [
            # Product queries
            "What is the price of OnePlus 10 Pro?",
            "Show me all products available",
            "Find products with high ratings",
            
            # Movie queries  
            "Show me movies with the highest ratings",
            "Find recent movies",
            "What movies are available?",
            
            # User/Customer queries
            "Show me user information",
            "How many customers do we have?",
            "Display customer data",
            
            # General exploration
            "What collections are available?",
            "What data is in the comics-generator database?",
            "Show me some sample data",
            
            # Analytics queries
            "Count total products",
            "Show me recent items",
            "Find items by category"
        ]
        
        print(f"\n📝 EXAMPLE QUESTIONS YOU CAN ASK:")
        print(f"{'='*50}")
        
        categories = {
            "🛍️ Product Questions": examples[:3],
            "🎬 Movie Questions": examples[3:6], 
            "👥 User/Customer Questions": examples[6:9],
            "🔍 Data Exploration": examples[9:12],
            "📊 Analytics Questions": examples[12:]
        }
        
        for category, questions in categories.items():
            print(f"\n{category}:")
            for question in questions:
                print(f"   • {question}")
    
    def run_demo_queries(self):
        """Run a set of demo queries to showcase capabilities"""
        if not self.is_initialized:
            print("❌ System not initialized. Please run initialize() first.")
            return
        
        demo_questions = [
            "What collections are available in comics-generator?",
            "What is the price of OnePlus 10 Pro?",
            "Show me movies with high ratings",
            "How many products are available?",
            "Show me user information"
        ]
        
        print(f"\n🎬 DEMO MODE - SHOWCASING CAPABILITIES")
        print(f"{'='*70}")
        print(f"Running {len(demo_questions)} demo queries...")
        
        for i, question in enumerate(demo_questions, 1):
            print(f"\n🎯 Demo {i}/{len(demo_questions)}: '{question}'")
            print("-" * 50)
            
            result = self.ask(question)
            
            if "error" not in result:
                print("✅ Query successful!")
            else:
                print(f"❌ Query failed: {result['error']}")
            
            # Pause between queries
            input("\nPress Enter to continue to next demo...")
        
        print(f"\n🎉 Demo completed! You can now ask your own questions.")


def main():
    """Main function"""
    print("🚀 Welcome to Ask Your Database!")
    print("The intelligent MongoDB assistant that answers your questions in natural language.")
    
    # Get MongoDB connection string
    connection_string = input("\nEnter MongoDB connection string (or press Enter for Atlas): ").strip()
    if not connection_string:
        # Default to Atlas cluster
        connection_string = "mongodb+srv://pkarthikeyan1202:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"
        print("🔗 Using Atlas cluster connection")
    
    # Initialize system
    assistant = AskYourDatabase()
    
    if not assistant.initialize(connection_string):
        print("❌ Failed to initialize system. Exiting.")
        return
    
    # Show menu
    while True:
        print(f"\n🎯 CHOOSE AN OPTION:")
        print("1. Interactive Mode (ask questions)")
        print("2. Demo Mode (see example queries)")
        print("3. Single Question")
        print("4. Show Cluster Stats")
        print("5. Exit")
        
        choice = input("\nEnter your choice (1-5): ").strip()
        
        if choice == '1':
            assistant.run_interactive_mode()
        elif choice == '2':
            assistant.run_demo_queries()
        elif choice == '3':
            question = input("Enter your question: ").strip()
            if question:
                assistant.ask(question)
        elif choice == '4':
            assistant.cluster_manager.print_cluster_summary()
        elif choice == '5':
            print("👋 Goodbye!")
            break
        else:
            print("Invalid choice. Please enter 1-5.")


if __name__ == "__main__":
    main()
