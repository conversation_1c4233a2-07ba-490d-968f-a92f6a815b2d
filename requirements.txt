# MongoDB Connection
pymongo==4.6.0

# Data Processing
pandas>=1.5.0
numpy>=1.24.0

# Visualization (Optional)
plotly>=5.15.0
streamlit>=1.28.0

# AI/ML Libraries
openai==1.35.0
langchain>=0.0.350
langchain-anthropic>=0.1.0

# Environment and Configuration
python-dotenv==1.0.0

# Development and Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0

# FastAPI (for future API development)
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Data Validation
pydantic==2.5.0
pydantic-settings==2.1.0

# Logging
structlog==23.2.0

# Optional but recommended
python-multipart==0.0.6